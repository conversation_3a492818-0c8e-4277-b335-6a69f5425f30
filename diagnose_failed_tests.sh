#!/bin/bash

# Diagnose Failed Tests - ExtremeLife MLM Production
# Detailed analysis of the 2 failed tests to identify root causes

echo "🔍 Diagnosing Failed Tests - ExtremeLife MLM"
echo "============================================"
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${YELLOW}Analyzing the 2 failed tests from verification:${NC}"
echo -e "${YELLOW}1. MLM directory accessible (HTTP 200)${NC}"
echo -e "${YELLOW}2. Member dashboard accessible (HTTP 200)${NC}"
echo ""

# FAILED TEST 1: MLM Directory Access
echo -e "${RED}🔍 FAILED TEST 1: MLM Directory Access${NC}"
echo "========================================"

echo "Testing MLM directory access..."
RESPONSE=$(curl -s -I https://extremelifeherbal.com/mlm/ 2>&1)
HTTP_CODE=$(echo "$RESPONSE" | grep -i "HTTP" | head -1)

echo "Full HTTP Response:"
echo "$RESPONSE"
echo ""

if echo "$RESPONSE" | grep -q "200 OK"; then
    echo -e "${GREEN}✅ MLM directory returns HTTP 200${NC}"
elif echo "$RESPONSE" | grep -q "301\|302"; then
    echo -e "${YELLOW}⚠️ MLM directory returns redirect${NC}"
    LOCATION=$(echo "$RESPONSE" | grep -i "location:" | head -1)
    echo "Redirect location: $LOCATION"
elif echo "$RESPONSE" | grep -q "403"; then
    echo -e "${RED}❌ MLM directory returns HTTP 403 (Forbidden)${NC}"
elif echo "$RESPONSE" | grep -q "404"; then
    echo -e "${RED}❌ MLM directory returns HTTP 404 (Not Found)${NC}"
elif echo "$RESPONSE" | grep -q "500"; then
    echo -e "${RED}❌ MLM directory returns HTTP 500 (Server Error)${NC}"
else
    echo -e "${RED}❌ MLM directory returns: $HTTP_CODE${NC}"
fi

echo ""
echo "Checking MLM index.php file:"
if [ -f "/var/www/html/mlm/index.php" ]; then
    echo -e "${GREEN}✅ /var/www/html/mlm/index.php exists${NC}"
    echo "File permissions: $(stat -c '%a' /var/www/html/mlm/index.php)"
    echo "File ownership: $(stat -c '%U:%G' /var/www/html/mlm/index.php)"
    echo "File size: $(stat -c '%s' /var/www/html/mlm/index.php) bytes"
    
    echo ""
    echo "File content preview:"
    head -10 /var/www/html/mlm/index.php
    
    echo ""
    echo "PHP syntax check:"
    php -l /var/www/html/mlm/index.php
else
    echo -e "${RED}❌ /var/www/html/mlm/index.php does not exist${NC}"
fi

echo ""
echo "Checking MLM directory permissions:"
if [ -d "/var/www/html/mlm" ]; then
    echo -e "${GREEN}✅ /var/www/html/mlm directory exists${NC}"
    echo "Directory permissions: $(stat -c '%a' /var/www/html/mlm)"
    echo "Directory ownership: $(stat -c '%U:%G' /var/www/html/mlm)"
    
    echo ""
    echo "Directory contents:"
    ls -la /var/www/html/mlm/ | head -10
else
    echo -e "${RED}❌ /var/www/html/mlm directory does not exist${NC}"
fi

echo ""
echo "Checking for .htaccess interference:"
if [ -f "/var/www/html/mlm/.htaccess" ]; then
    echo -e "${YELLOW}⚠️ Found .htaccess in MLM directory${NC}"
    echo "Content:"
    cat /var/www/html/mlm/.htaccess
else
    echo -e "${GREEN}✅ No .htaccess in MLM directory${NC}"
fi

echo ""
echo ""

# FAILED TEST 2: Member Dashboard Access
echo -e "${RED}🔍 FAILED TEST 2: Member Dashboard Access${NC}"
echo "=========================================="

echo "Testing member dashboard access..."
DASHBOARD_RESPONSE=$(curl -s -I https://extremelifeherbal.com/mlm/member_dashboard.php 2>&1)
DASHBOARD_HTTP_CODE=$(echo "$DASHBOARD_RESPONSE" | grep -i "HTTP" | head -1)

echo "Full HTTP Response:"
echo "$DASHBOARD_RESPONSE"
echo ""

if echo "$DASHBOARD_RESPONSE" | grep -q "200 OK"; then
    echo -e "${GREEN}✅ Member dashboard returns HTTP 200${NC}"
elif echo "$DASHBOARD_RESPONSE" | grep -q "301\|302"; then
    echo -e "${YELLOW}⚠️ Member dashboard returns redirect${NC}"
    DASHBOARD_LOCATION=$(echo "$DASHBOARD_RESPONSE" | grep -i "location:" | head -1)
    echo "Redirect location: $DASHBOARD_LOCATION"
elif echo "$DASHBOARD_RESPONSE" | grep -q "403"; then
    echo -e "${RED}❌ Member dashboard returns HTTP 403 (Forbidden)${NC}"
elif echo "$DASHBOARD_RESPONSE" | grep -q "404"; then
    echo -e "${RED}❌ Member dashboard returns HTTP 404 (Not Found)${NC}"
elif echo "$DASHBOARD_RESPONSE" | grep -q "500"; then
    echo -e "${RED}❌ Member dashboard returns HTTP 500 (Server Error)${NC}"
else
    echo -e "${RED}❌ Member dashboard returns: $DASHBOARD_HTTP_CODE${NC}"
fi

echo ""
echo "Checking member dashboard file:"
if [ -f "/var/www/html/mlm/member_dashboard.php" ]; then
    echo -e "${GREEN}✅ /var/www/html/mlm/member_dashboard.php exists${NC}"
    echo "File permissions: $(stat -c '%a' /var/www/html/mlm/member_dashboard.php)"
    echo "File ownership: $(stat -c '%U:%G' /var/www/html/mlm/member_dashboard.php)"
    echo "File size: $(stat -c '%s' /var/www/html/mlm/member_dashboard.php) bytes"
    
    echo ""
    echo "PHP syntax check:"
    php -l /var/www/html/mlm/member_dashboard.php
    
    echo ""
    echo "File content preview (first 20 lines):"
    head -20 /var/www/html/mlm/member_dashboard.php
else
    echo -e "${RED}❌ /var/www/html/mlm/member_dashboard.php does not exist${NC}"
    
    # Check if it exists elsewhere
    echo "Searching for member_dashboard.php in other locations:"
    find /var/www/html -name "member_dashboard.php" -type f 2>/dev/null
fi

echo ""
echo ""

# ADDITIONAL DIAGNOSTICS
echo -e "${BLUE}🔧 ADDITIONAL DIAGNOSTICS${NC}"
echo "=========================="

echo "Apache status:"
if sudo systemctl is-active --quiet apache2; then
    echo -e "${GREEN}✅ Apache is running${NC}"
else
    echo -e "${RED}❌ Apache is not running${NC}"
    sudo systemctl status apache2
fi

echo ""
echo "Apache error log (last 10 lines):"
sudo tail -10 /var/log/apache2/error.log

echo ""
echo "PHP error log (if exists):"
if [ -f "/var/log/php_errors.log" ]; then
    tail -10 /var/log/php_errors.log
elif [ -f "/var/log/apache2/php_errors.log" ]; then
    tail -10 /var/log/apache2/php_errors.log
else
    echo "No PHP error log found"
fi

echo ""
echo "Apache virtual host configuration:"
if [ -f "/etc/apache2/sites-available/000-default.conf" ]; then
    echo "DocumentRoot from default site:"
    grep DocumentRoot /etc/apache2/sites-available/000-default.conf
fi

echo ""
echo "Testing direct file access:"
echo "Attempting to access files directly via curl..."

# Test login.php (known working)
LOGIN_TEST=$(curl -s -I https://extremelifeherbal.com/mlm/login.php | grep -i "HTTP" | head -1)
echo "login.php: $LOGIN_TEST"

# Test register.php (known working)
REGISTER_TEST=$(curl -s -I https://extremelifeherbal.com/mlm/register.php | grep -i "HTTP" | head -1)
echo "register.php: $REGISTER_TEST"

# Test enhanced_cart.php (known working)
CART_TEST=$(curl -s -I https://extremelifeherbal.com/mlm/enhanced_cart.php | grep -i "HTTP" | head -1)
echo "enhanced_cart.php: $CART_TEST"

echo ""
echo ""

# RECOMMENDATIONS
echo -e "${YELLOW}📋 DIAGNOSTIC SUMMARY & RECOMMENDATIONS${NC}"
echo "========================================"

if echo "$RESPONSE" | grep -q "301\|302"; then
    echo -e "${BLUE}🔍 MLM Directory Issue: Redirect detected${NC}"
    echo "   Recommendation: Check index.php redirect logic"
    echo "   Action: Run fix_remaining_issues.sh to update index.php"
fi

if echo "$RESPONSE" | grep -q "403"; then
    echo -e "${BLUE}🔍 MLM Directory Issue: Access forbidden${NC}"
    echo "   Recommendation: Check directory permissions and .htaccess"
    echo "   Action: Verify no restrictive .htaccess rules"
fi

if echo "$RESPONSE" | grep -q "500"; then
    echo -e "${BLUE}🔍 MLM Directory Issue: Server error${NC}"
    echo "   Recommendation: Check Apache error logs and PHP syntax"
    echo "   Action: Review error logs above and fix PHP errors"
fi

if echo "$DASHBOARD_RESPONSE" | grep -q "301\|302"; then
    echo -e "${BLUE}🔍 Dashboard Issue: Redirect detected${NC}"
    echo "   Recommendation: This may be normal (auth redirect to login)"
    echo "   Action: Test with valid session or check redirect destination"
fi

if echo "$DASHBOARD_RESPONSE" | grep -q "500"; then
    echo -e "${BLUE}🔍 Dashboard Issue: Server error${NC}"
    echo "   Recommendation: Check PHP syntax and database connectivity"
    echo "   Action: Review PHP syntax check results above"
fi

echo ""
echo -e "${GREEN}🚀 NEXT STEPS:${NC}"
echo "1. Review diagnostic results above"
echo "2. Run: bash fix_remaining_issues.sh"
echo "3. Re-run: bash quick_verification_test.sh"
echo "4. Expected: 95%+ success rate"
