#!/bin/bash

# Test URL Redirects - ExtremeLife MLM Production
# Verifies that old URLs properly redirect to new /mlm/ locations

echo "🧪 Testing URL Redirects - ExtremeLife MLM"
echo "=========================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PASSED=0
FAILED=0
TOTAL=0

test_redirect() {
    TOTAL=$((TOTAL + 1))
    local old_url=$1
    local expected_new_path=$2
    local test_name=$3
    
    echo -e "${BLUE}Testing: $test_name${NC}"
    echo "Old URL: $old_url"
    
    # Get redirect response
    local response=$(curl -s -I "$old_url" 2>/dev/null)
    local http_code=$(echo "$response" | grep -i "HTTP" | head -1)
    local location=$(echo "$response" | grep -i "Location:" | head -1)
    
    echo "Response: $http_code"
    echo "Location: $location"
    
    if echo "$response" | grep -q "301\|302"; then
        if echo "$location" | grep -q "$expected_new_path"; then
            echo -e "${GREEN}✅ PASS: Redirects correctly${NC}"
            PASSED=$((PASSED + 1))
        else
            echo -e "${RED}❌ FAIL: Redirects to wrong location${NC}"
            FAILED=$((FAILED + 1))
        fi
    elif echo "$response" | grep -q "200 OK"; then
        # Check if it's serving content from the right location
        if echo "$location" | grep -q "$expected_new_path" || curl -s "$old_url" | grep -q "ExtremeLife"; then
            echo -e "${GREEN}✅ PASS: Serving content correctly${NC}"
            PASSED=$((PASSED + 1))
        else
            echo -e "${YELLOW}⚠️ PARTIAL: Returns 200 but may not be correct content${NC}"
            FAILED=$((FAILED + 1))
        fi
    else
        echo -e "${RED}❌ FAIL: Unexpected response${NC}"
        FAILED=$((FAILED + 1))
    fi
    
    echo ""
}

echo -e "${YELLOW}Testing old root URLs redirect to new /mlm/ locations...${NC}"
echo ""

# Test the problematic URLs mentioned by user
test_redirect "https://extremelifeherbal.com/enhanced_cart.php" "/mlm/enhanced_cart.php" "Enhanced Cart Redirect"
test_redirect "https://extremelifeherbal.com/member_dashboard.php" "/mlm/member_dashboard.php" "Member Dashboard Redirect"
test_redirect "https://extremelifeherbal.com/login.php" "/mlm/login.php" "Login Page Redirect"

# Test additional common URLs
test_redirect "https://extremelifeherbal.com/register.php" "/mlm/register.php" "Registration Page Redirect"
test_redirect "https://extremelifeherbal.com/genealogy_tree.php" "/mlm/genealogy_tree" "Genealogy Tree Redirect"

echo "========================================"
echo -e "${BLUE}🎯 REDIRECT TEST SUMMARY${NC}"
echo "========================================"
echo "Total Tests: $TOTAL"
echo "Passed: $PASSED"
echo "Failed: $FAILED"
echo "Success Rate: $(( PASSED * 100 / TOTAL ))%"
echo ""

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL REDIRECTS WORKING PERFECTLY!${NC}"
    echo -e "${GREEN}✅ Old URLs now redirect to /mlm/ directory${NC}"
    echo -e "${GREEN}✅ Users can access files from both locations${NC}"
    echo -e "${GREEN}✅ No more 404 Not Found errors${NC}"
    echo ""
    echo -e "${YELLOW}📋 VERIFIED WORKING URLS:${NC}"
    echo -e "${YELLOW}• https://extremelifeherbal.com/enhanced_cart.php → /mlm/enhanced_cart.php${NC}"
    echo -e "${YELLOW}• https://extremelifeherbal.com/member_dashboard.php → /mlm/member_dashboard.php${NC}"
    echo -e "${YELLOW}• https://extremelifeherbal.com/login.php → /mlm/login.php${NC}"
    echo -e "${YELLOW}• https://extremelifeherbal.com/register.php → /mlm/register.php${NC}"
    echo ""
    echo -e "${GREEN}🚀 URL REDIRECT FIX SUCCESSFUL!${NC}"
    
elif [ $FAILED -le 2 ]; then
    echo -e "${YELLOW}⚠️ Most redirects working ($FAILED issues)${NC}"
    echo -e "${YELLOW}📋 Minor issues detected - review failed tests${NC}"
    echo ""
    echo -e "${BLUE}🔧 TROUBLESHOOTING:${NC}"
    echo "1. Check if redirect files were created in /var/www/html/"
    echo "2. Verify Apache restarted successfully"
    echo "3. Check .htaccess file permissions"
    echo "4. Review Apache error logs"
    
else
    echo -e "${RED}🚨 Redirect system needs attention ($FAILED failures)${NC}"
    echo -e "${RED}📋 Multiple redirect issues detected${NC}"
    echo ""
    echo -e "${BLUE}🔧 TROUBLESHOOTING:${NC}"
    echo "1. Re-run: bash fix_url_redirects.sh"
    echo "2. Check Apache configuration: sudo apache2ctl configtest"
    echo "3. Verify file permissions: ls -la /var/www/html/*.php"
    echo "4. Check Apache error logs: sudo tail -f /var/log/apache2/error.log"
fi

echo ""
echo -e "${BLUE}📋 DIRECT ACCESS URLS (ALWAYS WORK):${NC}"
echo "• https://extremelifeherbal.com/mlm/login.php"
echo "• https://extremelifeherbal.com/mlm/register.php"
echo "• https://extremelifeherbal.com/mlm/member_dashboard.php"
echo "• https://extremelifeherbal.com/mlm/enhanced_cart.php"
echo "• https://extremelifeherbal.com/mlm/genealogy_tree_unilevel.php"
echo ""
echo -e "${YELLOW}💡 TIP: Use /mlm/ URLs for direct access, root URLs for redirects${NC}"
