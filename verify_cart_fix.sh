#!/bin/bash

# Verify Cart Fix - ExtremeLife MLM Production
# Quick verification that the emergency cart fix worked

echo "✅ Verifying Cart Fix - ExtremeLife MLM"
echo "======================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PASSED=0
FAILED=0
TOTAL=0

test_result() {
    TOTAL=$((TOTAL + 1))
    if [ $1 -eq 0 ]; then
        echo -e "✅ $2"
        PASSED=$((PASSED + 1))
    else
        echo -e "❌ $2"
        FAILED=$((FAILED + 1))
    fi
}

echo -e "${YELLOW}Testing cart after emergency fix...${NC}"
echo ""

# 1. Test direct cart access
echo -e "${BLUE}TEST 1: Direct Cart Access${NC}"
DIRECT_RESPONSE=$(curl -s -I https://extremelifeherbal.com/mlm/enhanced_cart.php 2>/dev/null)
if echo "$DIRECT_RESPONSE" | grep -q "200 OK"; then
    test_result 0 "Direct cart access returns HTTP 200"
else
    test_result 1 "Direct cart access failed"
    echo "Response: $(echo "$DIRECT_RESPONSE" | grep -i "HTTP" | head -1)"
fi

# 2. Test redirect cart access
echo -e "${BLUE}TEST 2: Redirect Cart Access${NC}"
REDIRECT_RESPONSE=$(curl -s -I https://extremelifeherbal.com/enhanced_cart.php 2>/dev/null)
if echo "$REDIRECT_RESPONSE" | grep -q "301\|302"; then
    test_result 0 "Redirect cart access works (301/302)"
else
    test_result 1 "Redirect cart access failed"
    echo "Response: $(echo "$REDIRECT_RESPONSE" | grep -i "HTTP" | head -1)"
fi

# 3. Test cart content
echo -e "${BLUE}TEST 3: Cart Content${NC}"
CART_CONTENT=$(curl -s https://extremelifeherbal.com/mlm/enhanced_cart.php 2>/dev/null)
if echo "$CART_CONTENT" | grep -q "ExtremeLife.*Cart"; then
    test_result 0 "Cart loads with ExtremeLife branding"
else
    test_result 1 "Cart content not loading properly"
fi

# 4. Test debug information
echo -e "${BLUE}TEST 4: Debug Information${NC}"
if echo "$CART_CONTENT" | grep -q "Debug Information\|Session ID"; then
    test_result 0 "Debug information is present"
else
    test_result 1 "Debug information missing"
fi

# 5. Test test functionality
echo -e "${BLUE}TEST 5: Test Functionality${NC}"
if echo "$CART_CONTENT" | grep -q "Add Test Product\|Test Cart Functionality"; then
    test_result 0 "Test functionality is available"
else
    test_result 1 "Test functionality missing"
fi

# 6. Test currency formatting
echo -e "${BLUE}TEST 6: Currency Formatting${NC}"
if echo "$CART_CONTENT" | grep -q "₱"; then
    test_result 0 "Philippine Peso currency formatting present"
else
    test_result 1 "Currency formatting missing"
fi

# 7. Test navigation
echo -e "${BLUE}TEST 7: Navigation Links${NC}"
if echo "$CART_CONTENT" | grep -q "Dashboard\|Login\|Home"; then
    test_result 0 "Navigation links present"
else
    test_result 1 "Navigation links missing"
fi

# 8. Test for PHP errors
echo -e "${BLUE}TEST 8: PHP Error Check${NC}"
if echo "$CART_CONTENT" | grep -q "Fatal error\|Parse error\|Warning.*error"; then
    test_result 1 "PHP errors detected"
else
    test_result 0 "No PHP errors detected"
fi

# 9. Test file permissions
echo -e "${BLUE}TEST 9: File Permissions${NC}"
if [ -f "/var/www/html/mlm/enhanced_cart.php" ]; then
    PERMS=$(stat -c '%a' /var/www/html/mlm/enhanced_cart.php)
    if [ "$PERMS" = "644" ]; then
        test_result 0 "File permissions are correct (644)"
    else
        test_result 1 "File permissions incorrect ($PERMS)"
    fi
else
    test_result 1 "Cart file does not exist"
fi

# 10. Test Apache status
echo -e "${BLUE}TEST 10: Apache Status${NC}"
if sudo systemctl is-active --quiet apache2; then
    test_result 0 "Apache is running"
else
    test_result 1 "Apache is not running"
fi

echo ""
echo "========================================"
echo -e "${BLUE}🎯 CART FIX VERIFICATION SUMMARY${NC}"
echo "========================================"
echo "Total Tests: $TOTAL"
echo "Passed: $PASSED"
echo "Failed: $FAILED"
echo "Success Rate: $(( PASSED * 100 / TOTAL ))%"
echo ""

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 CART FIX SUCCESSFUL!${NC}"
    echo -e "${GREEN}✅ All cart functionality tests passed${NC}"
    echo -e "${GREEN}✅ Cart is now fully operational${NC}"
    echo ""
    echo -e "${YELLOW}📋 MANUAL TESTING STEPS:${NC}"
    echo -e "${YELLOW}1. Visit: https://extremelifeherbal.com/enhanced_cart.php${NC}"
    echo -e "${YELLOW}2. Should redirect to /mlm/enhanced_cart.php${NC}"
    echo -e "${YELLOW}3. Click 'Add Test Product (₱100.00)' button${NC}"
    echo -e "${YELLOW}4. Verify test product appears in cart${NC}"
    echo -e "${YELLOW}5. Check debug info shows session data${NC}"
    echo -e "${YELLOW}6. Test 'Clear Cart' functionality${NC}"
    echo ""
    echo -e "${GREEN}🚀 Cart is ready for production use!${NC}"
    
elif [ $FAILED -le 2 ]; then
    echo -e "${YELLOW}⚠️ Cart mostly working ($FAILED minor issues)${NC}"
    echo -e "${YELLOW}📋 Review failed tests above${NC}"
    
else
    echo -e "${RED}🚨 Cart fix needs attention ($FAILED failures)${NC}"
    echo -e "${RED}📋 Multiple issues remain${NC}"
    echo ""
    echo -e "${BLUE}🔧 NEXT STEPS:${NC}"
    echo "1. Check Apache error logs: sudo tail -f /var/log/apache2/error.log"
    echo "2. Verify file exists: ls -la /var/www/html/mlm/enhanced_cart.php"
    echo "3. Test PHP syntax: php -l /var/www/html/mlm/enhanced_cart.php"
fi

echo ""
echo -e "${BLUE}📄 CART CONTENT PREVIEW:${NC}"
echo "First few lines of cart response:"
echo "$CART_CONTENT" | head -5

if echo "$CART_CONTENT" | grep -q "Debug Information"; then
    echo ""
    echo -e "${BLUE}🔍 DEBUG INFO FOUND:${NC}"
    echo "$CART_CONTENT" | grep -A 10 "Debug Information" | head -10
fi

echo ""
echo -e "${YELLOW}💡 TIP: The cart now includes test functionality and debug info for easy troubleshooting${NC}"
