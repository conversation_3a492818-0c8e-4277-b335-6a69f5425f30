# 🚨 CRITICAL PRODUCTION DEPLOYMENT COMMANDS

## **IMMEDIATE ACTION REQUIRED - ExtremeLife MLM System**

**Server:** *************** (extremelifeherbal.com)  
**Status:** 🔴 **52.9% FUNCTIONALITY - <PERSON><PERSON><PERSON><PERSON> FILES MISSING**  
**Urgency:** 🚨 **DEPLOY IMMEDIATELY**

---

## 📊 **CURRENT PRODUCTION STATUS**

### **✅ WORKING (9/17 tests passed)**
- Login system functional
- Admin interfaces accessible  
- Database connectivity working
- Authentication redirects working
- ExtremeLife branding present

### **❌ CRITICAL FAILURES (8/17 tests failed)**
- 🔴 **member_dashboard.php** - 404 NOT FOUND
- 🔴 **register.php** - 404 NOT FOUND  
- 🔴 **enhanced_cart.php** - 404 NOT FOUND
- 🔴 **checkout_confirmation.php** - 404 NOT FOUND
- 🔴 **MLM directory** - 403 FORBIDDEN
- 🔴 **Config files exposed** - Security risk
- 🔴 **Philippine Peso currency** - Not implemented

---

## 🚀 **EMERGENCY DEPLOYMENT COMMANDS**

### **STEP 1: SSH CONNECTION**
```bash
ssh mlmadmin@***************
# Password: 4K-6GsnA$3pQ5931
```

### **STEP 2: CREATE DIRECTORY STRUCTURE**
```bash
# Navigate to web directory
cd /var/www/html/mlm/

# Create missing directories
sudo mkdir -p config includes ecommerce

# Set proper ownership
sudo chown -R www-data:www-data /var/www/html/mlm/
```

### **STEP 3: DEPLOY CRITICAL FILES**

**Upload these files from local to production:**

1. **config/database.php** (from our emergency_config_database.php)
2. **includes/auth.php** (from our emergency_includes_auth.php)  
3. **member_dashboard.php** (from local fixed version)
4. **register.php** (from local fixed version)
5. **enhanced_cart.php** (from local fixed version)
6. **checkout_confirmation.php** (from local fixed version)

**SCP Upload Commands:**
```bash
# From local machine:
scp emergency_config_database.php mlmadmin@***************:/tmp/database.php
scp emergency_includes_auth.php mlmadmin@***************:/tmp/auth.php
scp member_dashboard.php mlmadmin@***************:/tmp/
scp register.php mlmadmin@***************:/tmp/
scp enhanced_cart.php mlmadmin@***************:/tmp/
scp checkout_confirmation.php mlmadmin@***************:/tmp/
```

### **STEP 4: MOVE FILES TO CORRECT LOCATIONS**
```bash
# On production server:
sudo mv /tmp/database.php /var/www/html/mlm/config/
sudo mv /tmp/auth.php /var/www/html/mlm/includes/
sudo mv /tmp/member_dashboard.php /var/www/html/mlm/
sudo mv /tmp/register.php /var/www/html/mlm/
sudo mv /tmp/enhanced_cart.php /var/www/html/mlm/
sudo mv /tmp/checkout_confirmation.php /var/www/html/mlm/
```

### **STEP 5: SET PROPER PERMISSIONS**
```bash
# Set file permissions
sudo chmod 644 /var/www/html/mlm/config/database.php
sudo chmod 644 /var/www/html/mlm/includes/auth.php
sudo chmod 644 /var/www/html/mlm/*.php

# Set directory permissions
sudo chmod 755 /var/www/html/mlm/config/
sudo chmod 755 /var/www/html/mlm/includes/
sudo chmod 755 /var/www/html/mlm/ecommerce/

# Set ownership
sudo chown -R www-data:www-data /var/www/html/mlm/
```

### **STEP 6: SECURE CONFIG FILES**
```bash
# Protect config directory from web access
sudo cat > /var/www/html/mlm/config/.htaccess << 'EOF'
Order deny,allow
Deny from all
EOF

# Protect includes directory from web access
sudo cat > /var/www/html/mlm/includes/.htaccess << 'EOF'
Order deny,allow
Deny from all
EOF

sudo chmod 644 /var/www/html/mlm/config/.htaccess
sudo chmod 644 /var/www/html/mlm/includes/.htaccess
```

### **STEP 7: FIX DIRECTORY ACCESS**
```bash
# Create index.php for MLM directory
sudo cat > /var/www/html/mlm/index.php << 'EOF'
<?php
// ExtremeLife MLM System - Main Directory
header('Location: login.php');
exit;
?>
EOF

sudo chmod 644 /var/www/html/mlm/index.php
sudo chown www-data:www-data /var/www/html/mlm/index.php
```

### **STEP 8: RESTART SERVICES**
```bash
# Restart Apache
sudo systemctl restart apache2

# Check Apache status
sudo systemctl status apache2

# Check for errors
sudo tail -f /var/log/apache2/error.log
```

---

## 🧪 **VERIFICATION TESTS**

### **After deployment, test these URLs:**
- ✅ https://extremelifeherbal.com/mlm/ (should redirect to login)
- ✅ https://extremelifeherbal.com/mlm/login.php
- ✅ https://extremelifeherbal.com/mlm/register.php
- ✅ https://extremelifeherbal.com/mlm/member_dashboard.php
- ✅ https://extremelifeherbal.com/mlm/enhanced_cart.php
- ✅ https://extremelifeherbal.com/mlm/checkout_confirmation.php

### **Security verification:**
- ❌ https://extremelifeherbal.com/mlm/config/database.php (should be 403)
- ❌ https://extremelifeherbal.com/mlm/includes/auth.php (should be 403)

---

## 📋 **POST-DEPLOYMENT CHECKLIST**

- [ ] All core files return HTTP 200
- [ ] Config files return HTTP 403 (secured)
- [ ] Authentication system working
- [ ] Database connectivity confirmed
- [ ] Philippine Peso currency displaying
- [ ] ExtremeLife branding intact
- [ ] Mobile responsiveness working
- [ ] Session management functional

---

## ⚡ **QUICK DEPLOYMENT SCRIPT**

```bash
#!/bin/bash
# Quick deployment script - run on production server

cd /var/www/html/mlm/
sudo mkdir -p config includes ecommerce
sudo chown -R www-data:www-data /var/www/html/mlm/

# Move uploaded files (assumes files are in /tmp/)
sudo mv /tmp/database.php config/
sudo mv /tmp/auth.php includes/
sudo mv /tmp/member_dashboard.php .
sudo mv /tmp/register.php .
sudo mv /tmp/enhanced_cart.php .
sudo mv /tmp/checkout_confirmation.php .

# Set permissions
sudo chmod 644 config/database.php includes/auth.php *.php
sudo chmod 755 config/ includes/ ecommerce/
sudo chown -R www-data:www-data /var/www/html/mlm/

# Secure directories
echo "Order deny,allow\nDeny from all" | sudo tee config/.htaccess includes/.htaccess

# Create index redirect
echo "<?php header('Location: login.php'); exit; ?>" | sudo tee index.php

sudo systemctl restart apache2
echo "Deployment complete!"
```

---

## 🎯 **SUCCESS METRICS**

**Target:** 100% functionality (17/17 tests passing)  
**Current:** 52.9% functionality (9/17 tests passing)  
**Missing:** 8 critical components

**After deployment, expect:**
- ✅ All core MLM functionality restored
- ✅ Member registration and login working
- ✅ E-commerce cart and checkout functional
- ✅ Security properly configured
- ✅ Full ExtremeLife branding with Philippine Peso

---

**🚨 DEPLOY IMMEDIATELY TO RESTORE FULL SYSTEM FUNCTIONALITY**
