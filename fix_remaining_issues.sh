#!/bin/bash

# Fix Remaining Issues - ExtremeLife MLM Production
# Addresses the 2 failed tests to achieve 95%+ functionality

echo "🔧 Fixing Remaining ExtremeLife MLM Issues"
echo "=========================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo -e "${YELLOW}Current Status: 90% functional (18/20 tests passing)${NC}"
echo -e "${YELLOW}Target: 95%+ functional (19+/20 tests passing)${NC}"
echo ""

# ISSUE 1: MLM Directory Access
print_info "ISSUE 1: Fixing MLM directory access..."

# Check current MLM index.php
if [ -f "/var/www/html/mlm/index.php" ]; then
    print_info "MLM index.php exists, checking content..."
    
    # Create improved index.php with better error handling
    sudo cat > /var/www/html/mlm/index.php << 'EOF'
<?php
/**
 * ExtremeLife MLM System - Main Directory Index
 * Enhanced with proper error handling and debugging
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session with error handling
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Log access attempt
error_log("MLM Directory accessed - Session ID: " . session_id());

// Check if user is already logged in
if (isset($_SESSION['member_id']) && !empty($_SESSION['member_id'])) {
    error_log("Redirecting logged-in user (ID: " . $_SESSION['member_id'] . ") to dashboard");
    header('Location: member_dashboard.php');
    exit;
}

// Redirect non-logged-in users to login
error_log("Redirecting non-logged-in user to login page");
header('Location: login.php');
exit;
?>
EOF

    # Set proper permissions
    sudo chown www-data:www-data /var/www/html/mlm/index.php
    sudo chmod 644 /var/www/html/mlm/index.php
    print_status "MLM index.php updated with enhanced error handling"
else
    print_warning "MLM index.php not found, creating new one..."
    
    # Create new index.php
    sudo cat > /var/www/html/mlm/index.php << 'EOF'
<?php
/**
 * ExtremeLife MLM System - Main Directory Index
 * Created to fix directory access issue
 */

session_start();

// Simple redirect to login
header('Location: login.php');
exit;
?>
EOF

    sudo chown www-data:www-data /var/www/html/mlm/index.php
    sudo chmod 644 /var/www/html/mlm/index.php
    print_status "New MLM index.php created"
fi

# ISSUE 2: Member Dashboard Access
print_info "ISSUE 2: Fixing member dashboard access..."

# Check if member_dashboard.php exists and has proper session handling
if [ -f "/var/www/html/mlm/member_dashboard.php" ]; then
    print_info "Member dashboard exists, checking for common issues..."
    
    # Check for PHP syntax errors
    php -l /var/www/html/mlm/member_dashboard.php > /tmp/dashboard_syntax_check 2>&1
    
    if grep -q "No syntax errors" /tmp/dashboard_syntax_check; then
        print_status "Member dashboard PHP syntax is valid"
    else
        print_warning "Member dashboard has PHP syntax errors:"
        cat /tmp/dashboard_syntax_check
    fi
    
    # Check file permissions
    PERMS=$(stat -c '%a' /var/www/html/mlm/member_dashboard.php)
    if [ "$PERMS" = "644" ]; then
        print_status "Member dashboard permissions are correct (644)"
    else
        print_info "Fixing member dashboard permissions..."
        sudo chmod 644 /var/www/html/mlm/member_dashboard.php
        print_status "Member dashboard permissions fixed"
    fi
    
    # Check ownership
    OWNER=$(stat -c '%U:%G' /var/www/html/mlm/member_dashboard.php)
    if [ "$OWNER" = "www-data:www-data" ]; then
        print_status "Member dashboard ownership is correct"
    else
        print_info "Fixing member dashboard ownership..."
        sudo chown www-data:www-data /var/www/html/mlm/member_dashboard.php
        print_status "Member dashboard ownership fixed"
    fi
    
else
    print_warning "Member dashboard not found in MLM directory!"
    
    # Check if it exists in root directory
    if [ -f "/var/www/html/member_dashboard.php" ]; then
        print_info "Found member dashboard in root, moving to MLM directory..."
        sudo mv /var/www/html/member_dashboard.php /var/www/html/mlm/
        sudo chown www-data:www-data /var/www/html/mlm/member_dashboard.php
        sudo chmod 644 /var/www/html/mlm/member_dashboard.php
        print_status "Member dashboard moved to MLM directory"
    else
        print_warning "Member dashboard not found anywhere - may need manual deployment"
    fi
fi

# ADDITIONAL FIXES: Check for common issues

print_info "ADDITIONAL CHECKS: Verifying common configuration issues..."

# Check Apache configuration
print_info "Checking Apache configuration..."
if sudo apache2ctl configtest > /tmp/apache_test 2>&1; then
    print_status "Apache configuration is valid"
else
    print_warning "Apache configuration issues detected:"
    cat /tmp/apache_test
fi

# Check PHP configuration
print_info "Checking PHP configuration..."
php -m | grep -q "session" && print_status "PHP session module loaded" || print_warning "PHP session module not found"
php -m | grep -q "pdo" && print_status "PHP PDO module loaded" || print_warning "PHP PDO module not found"
php -m | grep -q "pdo_mysql" && print_status "PHP PDO MySQL module loaded" || print_warning "PHP PDO MySQL module not found"

# Check .htaccess files don't interfere
print_info "Checking for interfering .htaccess files..."
if [ -f "/var/www/html/mlm/.htaccess" ]; then
    print_info "Found .htaccess in MLM directory, checking content..."
    if grep -q "RewriteEngine\|Redirect\|Deny" /var/www/html/mlm/.htaccess; then
        print_warning "MLM .htaccess may be interfering with access"
        print_info "Content of /var/www/html/mlm/.htaccess:"
        cat /var/www/html/mlm/.htaccess
    fi
else
    print_status "No interfering .htaccess in MLM directory"
fi

# Restart Apache to apply any changes
print_info "Restarting Apache to apply changes..."
sudo systemctl restart apache2

if sudo systemctl is-active --quiet apache2; then
    print_status "Apache restarted successfully"
else
    print_warning "Apache restart failed - checking status..."
    sudo systemctl status apache2
fi

# Clear any PHP opcache
print_info "Clearing PHP opcache if available..."
if php -m | grep -q "opcache"; then
    # Create a simple opcache reset script
    sudo cat > /tmp/reset_opcache.php << 'EOF'
<?php
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "OPcache reset successfully\n";
} else {
    echo "OPcache not available\n";
}
?>
EOF
    php /tmp/reset_opcache.php
    sudo rm /tmp/reset_opcache.php
else
    print_info "OPcache not installed"
fi

echo ""
echo -e "${GREEN}🎯 REMAINING ISSUES FIX COMPLETED${NC}"
echo -e "${GREEN}=================================${NC}"
echo ""
echo -e "${YELLOW}📋 CHANGES MADE:${NC}"
echo -e "${YELLOW}• Enhanced MLM index.php with better error handling${NC}"
echo -e "${YELLOW}• Verified member dashboard file location and permissions${NC}"
echo -e "${YELLOW}• Checked Apache and PHP configuration${NC}"
echo -e "${YELLOW}• Restarted Apache service${NC}"
echo -e "${YELLOW}• Cleared PHP opcache${NC}"
echo ""
echo -e "${BLUE}🧪 NEXT STEPS:${NC}"
echo -e "${BLUE}1. Run: bash quick_verification_test.sh${NC}"
echo -e "${BLUE}2. Expected result: 95%+ success rate (19+/20 tests)${NC}"
echo -e "${BLUE}3. Test URLs manually if needed:${NC}"
echo -e "${BLUE}   - https://extremelifeherbal.com/mlm/${NC}"
echo -e "${BLUE}   - https://extremelifeherbal.com/mlm/member_dashboard.php${NC}"
echo ""
echo -e "${GREEN}🚀 Target: Achieve 95%+ system functionality${NC}"

# Clean up temporary files
rm -f /tmp/dashboard_syntax_check /tmp/apache_test
