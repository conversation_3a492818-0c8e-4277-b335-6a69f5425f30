#!/bin/bash

# Diagnose Cart Issues - ExtremeLife MLM Production
# Identifies problems with cart functionality and session management

echo "🛒 Diagnosing Cart Issues - ExtremeLife MLM"
echo "==========================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo -e "${YELLOW}Issue: Cart shows empty after adding items${NC}"
echo -e "${YELLOW}Possible causes: Session issues, cart logic, database problems${NC}"
echo ""

# 1. Check cart file location and accessibility
print_info "STEP 1: Checking cart file locations..."

if [ -f "/var/www/html/mlm/enhanced_cart.php" ]; then
    print_status "enhanced_cart.php found in /mlm/ directory"
    echo "File size: $(stat -c '%s' /var/www/html/mlm/enhanced_cart.php) bytes"
    echo "Permissions: $(stat -c '%a' /var/www/html/mlm/enhanced_cart.php)"
    echo "Owner: $(stat -c '%U:%G' /var/www/html/mlm/enhanced_cart.php)"
else
    print_error "enhanced_cart.php not found in /mlm/ directory"
fi

if [ -f "/var/www/html/enhanced_cart.php" ]; then
    print_info "Redirect file exists in root directory"
else
    print_warning "No redirect file in root directory"
fi

echo ""

# 2. Check PHP syntax
print_info "STEP 2: Checking PHP syntax..."
if [ -f "/var/www/html/mlm/enhanced_cart.php" ]; then
    php -l /var/www/html/mlm/enhanced_cart.php > /tmp/cart_syntax_check 2>&1
    
    if grep -q "No syntax errors" /tmp/cart_syntax_check; then
        print_status "Cart PHP syntax is valid"
    else
        print_error "Cart PHP syntax errors detected:"
        cat /tmp/cart_syntax_check
    fi
else
    print_error "Cannot check syntax - file not found"
fi

echo ""

# 3. Check session configuration
print_info "STEP 3: Checking PHP session configuration..."

echo "Session save path: $(php -r 'echo session_save_path();')"
echo "Session module loaded: $(php -m | grep session || echo 'NOT FOUND')"

# Check session directory permissions
SESSION_PATH=$(php -r 'echo session_save_path();')
if [ -d "$SESSION_PATH" ]; then
    echo "Session directory exists: $SESSION_PATH"
    echo "Session directory permissions: $(stat -c '%a' $SESSION_PATH)"
    echo "Session directory owner: $(stat -c '%U:%G' $SESSION_PATH)"
    
    # Check if writable
    if [ -w "$SESSION_PATH" ]; then
        print_status "Session directory is writable"
    else
        print_error "Session directory is not writable"
    fi
else
    print_error "Session directory does not exist: $SESSION_PATH"
fi

echo ""

# 4. Check for cart-related files
print_info "STEP 4: Checking for cart-related files..."

# Look for product catalog or database files
if [ -f "/var/www/html/database_catalog.php" ]; then
    print_status "Product catalog found in root"
elif [ -f "/var/www/html/mlm/database_catalog.php" ]; then
    print_status "Product catalog found in /mlm/"
else
    print_warning "Product catalog not found"
fi

# Look for cart processing files
find /var/www/html -name "*cart*" -type f 2>/dev/null | head -10 | while read file; do
    echo "Found cart file: $file"
done

echo ""

# 5. Check database connectivity from cart
print_info "STEP 5: Testing database connectivity..."

# Create a simple database test script
cat > /tmp/test_db_connection.php << 'EOF'
<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=drupal_umd', 'drupal_user', 'secure_drupal_pass_1748318545');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n";
    
    // Test if products table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'mlm_products'");
    if ($stmt->rowCount() > 0) {
        echo "✅ mlm_products table exists\n";
        
        // Count products
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM mlm_products");
        $result = $stmt->fetch();
        echo "📦 Products in database: " . $result['count'] . "\n";
    } else {
        echo "❌ mlm_products table not found\n";
    }
    
    // Test if cart-related tables exist
    $tables = ['mlm_cart', 'mlm_orders', 'mlm_order_items'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ $table table exists\n";
        } else {
            echo "⚠️ $table table not found\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
}
?>
EOF

php /tmp/test_db_connection.php
rm /tmp/test_db_connection.php

echo ""

# 6. Check cart content and logic
print_info "STEP 6: Analyzing cart file content..."

if [ -f "/var/www/html/mlm/enhanced_cart.php" ]; then
    echo "Checking for session_start():"
    if grep -q "session_start()" /var/www/html/mlm/enhanced_cart.php; then
        print_status "session_start() found in cart file"
    else
        print_error "session_start() not found in cart file"
    fi
    
    echo "Checking for cart session variables:"
    if grep -q "\$_SESSION.*cart" /var/www/html/mlm/enhanced_cart.php; then
        print_status "Cart session variables found"
        echo "Cart session references:"
        grep -n "\$_SESSION.*cart" /var/www/html/mlm/enhanced_cart.php | head -5
    else
        print_warning "No cart session variables found"
    fi
    
    echo "Checking for database queries:"
    if grep -q "SELECT\|INSERT\|UPDATE" /var/www/html/mlm/enhanced_cart.php; then
        print_status "Database queries found in cart"
    else
        print_warning "No database queries found in cart"
    fi
    
    echo "Checking for error handling:"
    if grep -q "try\|catch\|error" /var/www/html/mlm/enhanced_cart.php; then
        print_status "Error handling found in cart"
    else
        print_warning "No error handling found in cart"
    fi
fi

echo ""

# 7. Check Apache error logs for cart-related errors
print_info "STEP 7: Checking Apache error logs for cart issues..."

echo "Recent Apache errors (last 20 lines):"
sudo tail -20 /var/log/apache2/error.log | grep -i "cart\|session\|php" || echo "No cart-related errors found"

echo ""

# 8. Test cart URL accessibility
print_info "STEP 8: Testing cart URL accessibility..."

# Test direct access
CART_RESPONSE=$(curl -s -I https://extremelifeherbal.com/mlm/enhanced_cart.php 2>/dev/null)
CART_HTTP_CODE=$(echo "$CART_RESPONSE" | grep -i "HTTP" | head -1)

echo "Direct cart access: $CART_HTTP_CODE"

# Test redirect access
REDIRECT_RESPONSE=$(curl -s -I https://extremelifeherbal.com/enhanced_cart.php 2>/dev/null)
REDIRECT_HTTP_CODE=$(echo "$REDIRECT_RESPONSE" | grep -i "HTTP" | head -1)

echo "Redirect cart access: $REDIRECT_HTTP_CODE"

echo ""

# 9. Check for common cart issues
print_info "STEP 9: Checking for common cart issues..."

# Check if there are any cart items in session (would need to be done via web interface)
echo "Common cart issues to check manually:"
echo "• Items not being added to session"
echo "• Session data being lost between pages"
echo "• Database connection issues"
echo "• Missing product data"
echo "• Incorrect cart logic"

echo ""
echo -e "${YELLOW}📋 DIAGNOSTIC SUMMARY${NC}"
echo "===================="

echo ""
echo -e "${BLUE}🔧 RECOMMENDED FIXES:${NC}"
echo "1. If session issues detected: Fix session permissions"
echo "2. If database issues found: Check database connectivity"
echo "3. If syntax errors found: Fix PHP syntax"
echo "4. If cart logic issues: Review cart session handling"
echo ""
echo -e "${GREEN}🚀 Next step: Run cart fix script based on findings${NC}"

# Clean up
rm -f /tmp/cart_syntax_check
