#!/bin/bash

# Session Debug Analysis for ExtremeLife MLM Cart Issue
# Deep dive into session management and cart persistence

echo "🔍 SESSION DEBUG ANALYSIS - ExtremeLife MLM"
echo "==========================================="
echo "Target: Identify exact cause of cart clearing issue"
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test session handling in cart
echo -e "${BLUE}📋 CART SESSION ANALYSIS${NC}"
echo "========================"

# Create session test script
cat > /tmp/test_cart_session.php << 'EOF'
<?php
session_start();

echo "=== CART SESSION DEBUG ===\n";
echo "Session ID: " . session_id() . "\n";
echo "Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "\n";
echo "Session Save Path: " . session_save_path() . "\n";

// Check current cart contents
echo "\n=== CURRENT CART CONTENTS ===\n";
if (isset($_SESSION['cart'])) {
    echo "Cart exists in session\n";
    echo "Cart contents: " . json_encode($_SESSION['cart']) . "\n";
    echo "Cart item count: " . count($_SESSION['cart']) . "\n";
} else {
    echo "No cart found in session\n";
}

// Test adding item to cart
echo "\n=== TESTING CART FUNCTIONALITY ===\n";
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Add test item
$_SESSION['cart']['test_item'] = [
    'name' => 'Debug Test Product',
    'price' => 100.00,
    'quantity' => 1
];

echo "Test item added to cart\n";
echo "Updated cart: " . json_encode($_SESSION['cart']) . "\n";

// Check session file
$session_file = session_save_path() . '/sess_' . session_id();
if (file_exists($session_file)) {
    echo "Session file exists: $session_file\n";
    echo "Session file size: " . filesize($session_file) . " bytes\n";
} else {
    echo "Session file not found: $session_file\n";
}

echo "\n=== SESSION VARIABLES ===\n";
foreach ($_SESSION as $key => $value) {
    echo "$key: " . (is_array($value) ? json_encode($value) : $value) . "\n";
}
?>
EOF

# Run session test
echo "Running cart session test..."
php /tmp/test_cart_session.php

echo -e "\n${BLUE}📋 CHECKOUT SESSION ANALYSIS${NC}"
echo "============================="

# Create checkout session test
cat > /tmp/test_checkout_session.php << 'EOF'
<?php
session_start();

echo "=== CHECKOUT SESSION DEBUG ===\n";
echo "Session ID: " . session_id() . "\n";

// Simulate cart data
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [
        'test_product' => [
            'name' => 'Test Product',
            'price' => 100.00,
            'quantity' => 1
        ]
    ];
    echo "Created test cart data\n";
}

echo "Cart before processing: " . json_encode($_SESSION['cart']) . "\n";

// Test the cart persistence logic from checkout
$cart_items = [];
$total_amount = 0;

if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
    foreach ($_SESSION['cart'] as $item_key => $item) {
        if (is_array($item)) {
            $price = $item['price'] ?? 100.00;
            $quantity = $item['quantity'] ?? 1;
            $subtotal = $price * $quantity;
            
            $cart_items[] = [
                'name' => $item['name'] ?? 'Test Product',
                'price' => $price,
                'quantity' => $quantity,
                'subtotal' => $subtotal
            ];
            
            $total_amount += $subtotal;
        }
    }
}

echo "Processed cart items: " . count($cart_items) . "\n";
echo "Total amount: $total_amount\n";

// Test persistence logic
$_SESSION['last_order_items'] = $cart_items;
$_SESSION['last_order_total'] = $total_amount;

echo "Stored in last_order_items: " . json_encode($_SESSION['last_order_items']) . "\n";

// Simulate order completion
$order_completed = true;
if ($order_completed) {
    echo "Order completed - cart should persist for confirmation\n";
    echo "Cart after order completion: " . json_encode($_SESSION['cart']) . "\n";
    echo "Last order items: " . json_encode($_SESSION['last_order_items']) . "\n";
}
?>
EOF

echo "Running checkout session test..."
php /tmp/test_checkout_session.php

echo -e "\n${BLUE}📋 SESSION CONFIGURATION ANALYSIS${NC}"
echo "=================================="

# Check PHP session configuration
echo "PHP Session Configuration:"
php -r "
echo 'session.save_handler: ' . ini_get('session.save_handler') . \"\n\";
echo 'session.save_path: ' . ini_get('session.save_path') . \"\n\";
echo 'session.use_cookies: ' . ini_get('session.use_cookies') . \"\n\";
echo 'session.cookie_lifetime: ' . ini_get('session.cookie_lifetime') . \"\n\";
echo 'session.gc_maxlifetime: ' . ini_get('session.gc_maxlifetime') . \"\n\";
echo 'session.gc_probability: ' . ini_get('session.gc_probability') . \"\n\";
echo 'session.gc_divisor: ' . ini_get('session.gc_divisor') . \"\n\";
"

# Check session directory permissions
SESSION_PATH=$(php -r 'echo session_save_path();')
echo -e "\nSession Directory Analysis:"
echo "Path: $SESSION_PATH"
if [ -d "$SESSION_PATH" ]; then
    echo "Directory exists: Yes"
    echo "Permissions: $(stat -c '%a' "$SESSION_PATH")"
    echo "Owner: $(stat -c '%U:%G' "$SESSION_PATH")"
    echo "Writable by www-data: $(sudo -u www-data test -w "$SESSION_PATH" && echo 'Yes' || echo 'No')"
    echo "Session files count: $(ls -1 "$SESSION_PATH"/sess_* 2>/dev/null | wc -l)"
else
    echo "Directory exists: No"
fi

echo -e "\n${BLUE}📋 REDIRECT CHAIN ANALYSIS${NC}"
echo "=========================="

# Test redirect chain that might be causing session loss
echo "Testing redirect chain for session persistence..."

# Test cart redirect
echo "1. Testing cart redirect:"
CART_REDIRECT=$(curl -s -I https://extremelifeherbal.com/enhanced_cart.php)
echo "$CART_REDIRECT" | grep -E "HTTP|Location"

# Test if redirect preserves session
echo -e "\n2. Testing session preservation through redirects:"
COOKIE_JAR="/tmp/session_test_cookies"
rm -f "$COOKIE_JAR"

# First request to establish session
curl -s -c "$COOKIE_JAR" https://extremelifeherbal.com/enhanced_cart.php > /dev/null
echo "Session established"

# Follow redirect with cookies
FINAL_RESPONSE=$(curl -s -b "$COOKIE_JAR" -I https://extremelifeherbal.com/mlm/enhanced_cart.php)
echo "Final response after redirect:"
echo "$FINAL_RESPONSE" | grep -E "HTTP|Set-Cookie"

# Clean up
rm -f "$COOKIE_JAR"

echo -e "\n${BLUE}📋 CODE LOGIC ANALYSIS${NC}"
echo "======================"

# Analyze the actual cart clearing logic in checkout
echo "Analyzing cart clearing logic in checkout file..."

if [ -f "/var/www/html/mlm/checkout_confirmation.php" ]; then
    echo "Checking for cart clearing statements:"
    
    # Look for cart clearing code
    CART_CLEAR_LINES=$(grep -n "cart.*=.*\[\]\|unset.*cart\|session_destroy" /var/www/html/mlm/checkout_confirmation.php 2>/dev/null)
    if [ -n "$CART_CLEAR_LINES" ]; then
        echo "Found cart clearing code:"
        echo "$CART_CLEAR_LINES"
    else
        echo "No explicit cart clearing code found"
    fi
    
    # Check for session_start placement
    SESSION_START_LINE=$(grep -n "session_start" /var/www/html/mlm/checkout_confirmation.php 2>/dev/null | head -1)
    if [ -n "$SESSION_START_LINE" ]; then
        echo "session_start() found at: $SESSION_START_LINE"
    else
        echo "session_start() not found - CRITICAL ISSUE"
    fi
    
    # Check for cart persistence logic
    PERSISTENCE_LOGIC=$(grep -n "last_order_items\|last_order_total" /var/www/html/mlm/checkout_confirmation.php 2>/dev/null)
    if [ -n "$PERSISTENCE_LOGIC" ]; then
        echo "Cart persistence logic found:"
        echo "$PERSISTENCE_LOGIC"
    else
        echo "Cart persistence logic missing - CRITICAL ISSUE"
    fi
else
    echo "Checkout file not found - CRITICAL ISSUE"
fi

echo -e "\n${BLUE}📋 LIVE TESTING SIMULATION${NC}"
echo "=========================="

# Simulate the actual user flow
echo "Simulating user cart-to-checkout flow..."

# Create test script that simulates user actions
cat > /tmp/simulate_user_flow.php << 'EOF'
<?php
echo "=== SIMULATING USER CART-TO-CHECKOUT FLOW ===\n";

// Step 1: Start session and add item to cart (like in cart page)
session_start();
echo "Step 1: Session started, ID: " . session_id() . "\n";

$_SESSION['cart'] = [
    'test_product' => [
        'name' => 'Test Product',
        'price' => 100.00,
        'quantity' => 1
    ]
];
echo "Step 2: Item added to cart\n";
echo "Cart contents: " . json_encode($_SESSION['cart']) . "\n";

// Step 3: Simulate going to checkout (new page load)
echo "\nStep 3: Simulating checkout page load...\n";

// This is what happens in checkout_confirmation.php
$cart_items = [];
$total_amount = 0;

if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
    echo "Cart found in session during checkout\n";
    foreach ($_SESSION['cart'] as $item_key => $item) {
        if (is_array($item)) {
            $price = $item['price'] ?? 100.00;
            $quantity = $item['quantity'] ?? 1;
            $subtotal = $price * $quantity;
            
            $cart_items[] = [
                'name' => $item['name'] ?? 'Test Product',
                'price' => $price,
                'quantity' => $quantity,
                'subtotal' => $subtotal
            ];
            
            $total_amount += $subtotal;
        }
    }
    echo "Processed " . count($cart_items) . " items, total: $total_amount\n";
} else {
    echo "ERROR: Cart not found in session during checkout!\n";
}

// Step 4: Simulate order processing
echo "\nStep 4: Simulating order processing...\n";

// Store cart items before processing (persistence logic)
$_SESSION['last_order_items'] = $cart_items;
$_SESSION['last_order_total'] = $total_amount;
echo "Cart items stored in last_order_items\n";

// Simulate successful order
$order_completed = true;
echo "Order completed: " . ($order_completed ? 'Yes' : 'No') . "\n";

// Check what happens to cart after order completion
echo "\nStep 5: Checking cart state after order completion...\n";
echo "Original cart: " . json_encode($_SESSION['cart'] ?? 'not set') . "\n";
echo "Last order items: " . json_encode($_SESSION['last_order_items'] ?? 'not set') . "\n";

// This is where the issue might be - premature cart clearing
echo "\nStep 6: Testing cart clearing logic...\n";
if ($order_completed && !isset($_GET['keep_cart'])) {
    echo "Cart would be cleared here (this might be the issue)\n";
    // $_SESSION['cart'] = []; // This line might be executing too early
} else {
    echo "Cart preserved\n";
}

echo "\nFinal cart state: " . json_encode($_SESSION['cart'] ?? 'not set') . "\n";
echo "Final last_order_items: " . json_encode($_SESSION['last_order_items'] ?? 'not set') . "\n";
?>
EOF

php /tmp/simulate_user_flow.php

# Clean up temporary files
rm -f /tmp/test_cart_session.php /tmp/test_checkout_session.php /tmp/simulate_user_flow.php

echo -e "\n${GREEN}🎯 SESSION DEBUG ANALYSIS COMPLETE${NC}"
echo "=================================="
echo "Review the output above to identify the exact cause of cart clearing."
echo ""
echo "Common issues to look for:"
echo "1. Session not starting properly"
echo "2. Cart being cleared before confirmation display"
echo "3. Session data lost during redirects"
echo "4. Session directory permission issues"
echo "5. Conflicting session management between cart and checkout"
