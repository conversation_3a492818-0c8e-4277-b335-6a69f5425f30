#!/bin/bash

# ExtremeLife MLM Emergency Production Deployment Script
# Server: *************** (extremelifeherbal.com)
# Purpose: Fix critical production issues immediately

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Server configuration
SERVER="***************"
USER="mlmadmin"
PASSWORD="4K-6GsnA$3pQ5931"
WEB_DIR="/var/www/html/mlm"

echo -e "${RED}🚨 EXTREMELIFE MLM EMERGENCY PRODUCTION DEPLOYMENT${NC}"
echo -e "${RED}=================================================${NC}"
echo ""
echo -e "${YELLOW}Server: $SERVER${NC}"
echo -e "${YELLOW}Target Directory: $WEB_DIR${NC}"
echo -e "${YELLOW}Timestamp: $(date)${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Step 1: Create emergency config/database.php
print_info "Creating emergency database configuration..."
cat > emergency_config_database.php << 'EOF'
<?php
/**
 * ExtremeLife MLM Database Configuration - PRODUCTION
 * Emergency deployment fix
 */

return [
    'host' => 'localhost',
    'dbname' => 'drupal_umd',
    'username' => 'drupal_user',
    'password' => 'secure_drupal_pass_1748318545',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];
?>
EOF

# Step 2: Create emergency includes/auth.php
print_info "Creating emergency authentication helper..."
cat > emergency_includes_auth.php << 'EOF'
<?php
/**
 * ExtremeLife MLM Authentication Helper - PRODUCTION
 * Emergency deployment fix
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['member_id']) && !empty($_SESSION['member_id']);
}

/**
 * Get current logged in member ID
 */
function getCurrentMemberId() {
    return $_SESSION['member_id'] ?? null;
}

/**
 * Get current logged in member info
 */
function getCurrentMember() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=drupal_umd', 'drupal_user', 'secure_drupal_pass_1748318545');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("SELECT * FROM mlm_members WHERE id = ? AND status = 'active'");
        $stmt->execute([$_SESSION['member_id']]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        error_log("Error getting current member: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if user has admin privileges
 */
function isAdmin() {
    if (!isLoggedIn()) {
        return false;
    }
    
    $member = getCurrentMember();
    return $member && ($member['user_role'] === 'admin' || $member['user_role'] === 'super_admin');
}

/**
 * Require login - redirect to login page if not logged in
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

/**
 * Get member info by ID
 */
function getMemberInfo($member_id) {
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=drupal_umd', 'drupal_user', 'secure_drupal_pass_1748318545');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT m.*, ug.group_name, ug.commission_rate as group_commission_rate
            FROM mlm_members m
            LEFT JOIN mlm_user_groups ug ON m.user_group_id = ug.id
            WHERE m.id = ?
        ");
        $stmt->execute([$member_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return [
                'id' => $member_id,
                'first_name' => 'Member',
                'last_name' => '',
                'user_group_id' => 1,
                'group_name' => 'Member',
                'group_commission_rate' => 10
            ];
        }
        
        return $result;
        
    } catch (PDOException $e) {
        error_log("Error getting member info: " . $e->getMessage());
        return [
            'id' => $member_id,
            'first_name' => 'Member',
            'last_name' => '',
            'user_group_id' => 1,
            'group_name' => 'Member',
            'group_commission_rate' => 10
        ];
    }
}

/**
 * Format currency for Philippine Peso
 */
function formatCurrency($amount) {
    if (is_null($amount) || $amount === '') {
        return '₱0.00';
    }
    return '₱' . number_format((float)$amount, 2);
}

/**
 * Format number safely
 */
function formatNumber($number, $decimals = 0) {
    if (is_null($number) || $number === '') {
        return '0';
    }
    return number_format((float)$number, $decimals);
}

/**
 * Safe HTML output
 */
function safeOutput($text) {
    return htmlspecialchars($text ?? '', ENT_QUOTES, 'UTF-8');
}
?>
EOF

print_status "Emergency files created locally"

# Step 3: Create deployment commands
print_info "Creating SSH deployment commands..."

cat > deploy_commands.txt << EOF
# SSH Commands to execute on production server
# Connect: ssh mlmadmin@***************

# 1. Create directory structure
sudo mkdir -p $WEB_DIR/config
sudo mkdir -p $WEB_DIR/includes
sudo mkdir -p $WEB_DIR/ecommerce

# 2. Set proper ownership
sudo chown -R www-data:www-data $WEB_DIR/

# 3. Upload files (use SCP or manual copy)
# scp emergency_config_database.php mlmadmin@***************:$WEB_DIR/config/database.php
# scp emergency_includes_auth.php mlmadmin@***************:$WEB_DIR/includes/auth.php

# 4. Set permissions
sudo chmod 644 $WEB_DIR/config/database.php
sudo chmod 644 $WEB_DIR/includes/auth.php
sudo chmod 755 $WEB_DIR/config/
sudo chmod 755 $WEB_DIR/includes/

# 5. Check PHP session configuration
php -i | grep session.save_path
sudo chmod 755 /var/lib/php/sessions/
sudo chown www-data:www-data /var/lib/php/sessions/

# 6. Restart Apache
sudo systemctl restart apache2

# 7. Check Apache error logs
sudo tail -f /var/log/apache2/error.log
EOF

print_status "Deployment commands created"

echo ""
echo -e "${YELLOW}📋 MANUAL DEPLOYMENT STEPS:${NC}"
echo -e "${YELLOW}===========================${NC}"
echo ""
echo "1. Upload emergency files to production server:"
echo "   scp emergency_config_database.php mlmadmin@$SERVER:$WEB_DIR/config/database.php"
echo "   scp emergency_includes_auth.php mlmadmin@$SERVER:$WEB_DIR/includes/auth.php"
echo ""
echo "2. SSH to server and execute:"
echo "   ssh mlmadmin@$SERVER"
echo "   sudo chown -R www-data:www-data $WEB_DIR/"
echo "   sudo chmod 644 $WEB_DIR/config/database.php"
echo "   sudo chmod 644 $WEB_DIR/includes/auth.php"
echo ""
echo "3. Copy missing core files from local to production:"
echo "   - member_dashboard.php"
echo "   - register.php" 
echo "   - enhanced_cart.php"
echo "   - Fixed genealogy_tree_unilevel.php"
echo ""
echo "4. Test critical URLs after deployment"
echo ""

print_warning "CRITICAL: Deploy these files immediately to restore system functionality!"

echo ""
echo -e "${GREEN}Emergency deployment script completed.${NC}"
echo -e "${GREEN}Files ready for manual upload to production server.${NC}"
