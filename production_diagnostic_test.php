<?php
/**
 * ExtremeLife MLM Production Server Diagnostic Test
 * Tests live production server functionality
 */

echo "🔍 ExtremeLife MLM Production Server Diagnostic Test\n";
echo "===================================================\n\n";

$base_url = "https://extremelifeherbal.com/mlm/";
$total_tests = 0;
$passed_tests = 0;
$errors = [];

function testUrl($url, $test_name, $expected_status = 200) {
    global $total_tests, $passed_tests, $errors;
    $total_tests++;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'ExtremeLife MLM Diagnostic Tool');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ $test_name - CURL Error: $error\n";
        $errors[] = "$test_name - CURL Error: $error";
        return false;
    }
    
    if ($http_code === $expected_status) {
        echo "✅ $test_name - HTTP $http_code\n";
        $passed_tests++;
        return true;
    } else {
        echo "❌ $test_name - HTTP $http_code (expected $expected_status)\n";
        $errors[] = "$test_name - HTTP $http_code (expected $expected_status)";
        return false;
    }
}

function testUrlContent($url, $test_name, $expected_content) {
    global $total_tests, $passed_tests, $errors;
    $total_tests++;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'ExtremeLife MLM Diagnostic Tool');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ $test_name - CURL Error: $error\n";
        $errors[] = "$test_name - CURL Error: $error";
        return false;
    }
    
    if ($http_code !== 200) {
        echo "❌ $test_name - HTTP $http_code\n";
        $errors[] = "$test_name - HTTP $http_code";
        return false;
    }
    
    if (strpos($response, $expected_content) !== false) {
        echo "✅ $test_name - Content found\n";
        $passed_tests++;
        return true;
    } else {
        echo "❌ $test_name - Expected content not found\n";
        $errors[] = "$test_name - Expected content not found";
        return false;
    }
}

echo "🌐 TESTING CORE URL ACCESSIBILITY\n";
echo "=================================\n";

// Test core URLs
testUrl($base_url, "MLM Directory Access", 200);
testUrl($base_url . "login.php", "Login Page");
testUrl($base_url . "register.php", "Registration Page");
testUrl($base_url . "member_dashboard.php", "Member Dashboard");
testUrl($base_url . "genealogy_tree_unilevel.php", "Genealogy Tree");
testUrl($base_url . "enhanced_cart.php", "Enhanced Cart");
testUrl($base_url . "checkout_confirmation.php", "Checkout Confirmation");

echo "\n🔧 TESTING ADMIN INTERFACES\n";
echo "===========================\n";

testUrl($base_url . "admin/dashboard.php", "Admin Dashboard");
testUrl($base_url . "admin/login.php", "Admin Login");
testUrl($base_url . "admin/member_management.php", "Member Management");

echo "\n📁 TESTING CONFIGURATION FILES\n";
echo "===============================\n";

// Test if config files are accessible (should be 403 or 404 for security)
testUrl($base_url . "config/database.php", "Database Config Security", 403);
testUrl($base_url . "includes/auth.php", "Auth Helper Security", 403);

echo "\n🎨 TESTING BRANDING AND CONTENT\n";
echo "===============================\n";

// Test for ExtremeLife branding
testUrlContent($base_url . "login.php", "ExtremeLife Branding", "ExtremeLife");
testUrlContent($base_url . "login.php", "Philippine Peso Currency", "₱");
testUrlContent($base_url . "login.php", "Green Color Scheme", "#2d5a27");

echo "\n🔐 TESTING AUTHENTICATION FLOW\n";
echo "==============================\n";

// Test authentication redirects
testUrlContent($base_url . "genealogy_tree_unilevel.php", "Authentication Redirect", "login");

echo "\n📊 TESTING DATABASE CONNECTIVITY\n";
echo "================================\n";

// Test pages that require database
testUrlContent($base_url . "login.php", "Database Connection", "Database Connected");

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 DIAGNOSTIC SUMMARY\n";
echo str_repeat("=", 60) . "\n";

echo "Total Tests: $total_tests\n";
echo "Passed: $passed_tests\n";
echo "Failed: " . ($total_tests - $passed_tests) . "\n";
echo "Success Rate: " . round(($passed_tests / $total_tests) * 100, 1) . "%\n\n";

if (count($errors) > 0) {
    echo "🚨 ISSUES FOUND:\n";
    echo "================\n";
    foreach ($errors as $error) {
        echo "• $error\n";
    }
    echo "\n";
}

if ($passed_tests === $total_tests) {
    echo "🎉 ALL TESTS PASSED! Production server is functioning correctly.\n";
} else {
    echo "⚠️ Issues detected. Review errors above and deploy fixes.\n";
}

echo "\n📋 RECOMMENDED ACTIONS:\n";
echo "======================\n";

if (in_array("Member Dashboard - HTTP 404 (expected 200)", $errors)) {
    echo "🔴 CRITICAL: Deploy member_dashboard.php to production\n";
}

if (in_array("Registration Page - HTTP 404 (expected 200)", $errors)) {
    echo "🔴 CRITICAL: Deploy register.php to production\n";
}

if (in_array("Enhanced Cart - HTTP 404 (expected 200)", $errors)) {
    echo "🔴 CRITICAL: Deploy enhanced_cart.php to production\n";
}

if (strpos(implode(' ', $errors), 'Authentication Redirect') !== false) {
    echo "🟡 HIGH: Fix authentication system and session management\n";
}

if (strpos(implode(' ', $errors), 'Database Connection') !== false) {
    echo "🟡 HIGH: Check database connectivity and configuration\n";
}

echo "\n🚀 Next Steps:\n";
echo "1. Deploy missing core files to /var/www/html/mlm/\n";
echo "2. Upload config/database.php and includes/auth.php\n";
echo "3. Set proper file permissions (644 for PHP, 755 for directories)\n";
echo "4. Test authentication flow\n";
echo "5. Verify database connectivity\n";
echo "6. Re-run this diagnostic test\n";
?>
