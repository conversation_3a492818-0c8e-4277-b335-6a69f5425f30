#!/bin/bash

# Comprehensive ExtremeLife MLM Cart & Checkout System Review
# Identifies and resolves persistent "cart empty after complete order" issue

echo "🔍 COMPREHENSIVE EXTREMELIFE MLM CART & CHECKOUT REVIEW"
echo "======================================================="
echo "Target: Resolve persistent 'cart empty after complete order' issue"
echo "Server: *************** (extremelifeherbal.com)"
echo "Date: $(date)"
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

ISSUES_FOUND=0
CRITICAL_ISSUES=()
WARNINGS=()

log_issue() {
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
    if [ "$2" = "critical" ]; then
        CRITICAL_ISSUES+=("$1")
        echo -e "${RED}❌ CRITICAL: $1${NC}"
    else
        WARNINGS+=("$1")
        echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
    fi
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# SECTION 1: COMPLETE CART-TO-CHECKOUT FLOW TEST
echo -e "${PURPLE}📋 SECTION 1: COMPLETE CART-TO-CHECKOUT FLOW TEST${NC}"
echo "=================================================="

# Test 1.1: Cart Page Accessibility
echo "TEST 1.1: Cart Page Accessibility"
CART_RESPONSE=$(curl -s -I https://extremelifeherbal.com/enhanced_cart.php 2>/dev/null)
CART_HTTP=$(echo "$CART_RESPONSE" | grep -i "HTTP" | head -1)
echo "Cart Response: $CART_HTTP"

if echo "$CART_RESPONSE" | grep -q "301\|302"; then
    log_success "Cart redirect working"
    
    # Test MLM cart directly
    MLM_CART_RESPONSE=$(curl -s -I https://extremelifeherbal.com/mlm/enhanced_cart.php 2>/dev/null)
    MLM_CART_HTTP=$(echo "$MLM_CART_RESPONSE" | grep -i "HTTP" | head -1)
    echo "MLM Cart Response: $MLM_CART_HTTP"
    
    if echo "$MLM_CART_RESPONSE" | grep -q "200 OK"; then
        log_success "MLM cart accessible"
    else
        log_issue "MLM cart not accessible" "critical"
    fi
else
    log_issue "Cart redirect not working" "critical"
fi

# Test 1.2: Cart Content and Test Functionality
echo -e "\nTEST 1.2: Cart Content and Test Functionality"
CART_CONTENT=$(curl -s https://extremelifeherbal.com/mlm/enhanced_cart.php 2>/dev/null)

if echo "$CART_CONTENT" | grep -q "Add Test Product"; then
    log_success "Cart test functionality available"
else
    log_issue "Cart test functionality missing" "critical"
fi

if echo "$CART_CONTENT" | grep -q "Proceed to Checkout\|checkout_confirmation"; then
    log_success "Checkout link present in cart"
else
    log_issue "Checkout link missing from cart" "critical"
fi

# Test 1.3: Checkout Page Accessibility
echo -e "\nTEST 1.3: Checkout Page Accessibility"
CHECKOUT_RESPONSE=$(curl -s -I https://extremelifeherbal.com/mlm/checkout_confirmation.php 2>/dev/null)
CHECKOUT_HTTP=$(echo "$CHECKOUT_RESPONSE" | grep -i "HTTP" | head -1)
echo "Checkout Response: $CHECKOUT_HTTP"

if echo "$CHECKOUT_RESPONSE" | grep -q "200 OK"; then
    log_success "Checkout page accessible"
else
    log_issue "Checkout page not accessible" "critical"
fi

# Test 1.4: Checkout Content Analysis
echo -e "\nTEST 1.4: Checkout Content Analysis"
CHECKOUT_CONTENT=$(curl -s https://extremelifeherbal.com/mlm/checkout_confirmation.php 2>/dev/null)

# Check for PHP errors
if echo "$CHECKOUT_CONTENT" | grep -q "Fatal error\|Parse error\|Warning.*error"; then
    log_issue "PHP errors detected in checkout" "critical"
    echo "Errors found:"
    echo "$CHECKOUT_CONTENT" | grep -i "error\|warning" | head -3
else
    log_success "No PHP errors in checkout"
fi

# Check for Complete Order button
if echo "$CHECKOUT_CONTENT" | grep -q "Complete Order"; then
    log_success "Complete Order button present"
else
    log_issue "Complete Order button missing" "critical"
fi

# Check for rebate calculation
if echo "$CHECKOUT_CONTENT" | grep -q "4\.00%.*rebate"; then
    log_success "4.00% rebate calculation displayed"
else
    log_issue "Rebate calculation not displayed" "warning"
fi

# Check for GCash payment option
if echo "$CHECKOUT_CONTENT" | grep -q "***********.*Evelyn Percil"; then
    log_success "GCash payment option with contact details found"
else
    log_issue "GCash payment option missing or incomplete" "warning"
fi

echo ""

# SECTION 2: DUPLICATE FILES AND CONFLICTING CODE ANALYSIS
echo -e "${PURPLE}📋 SECTION 2: DUPLICATE FILES AND CONFLICTING CODE ANALYSIS${NC}"
echo "==========================================================="

# Test 2.1: Check for duplicate cart files
echo "TEST 2.1: Duplicate Cart Files Check"
echo "Searching for cart-related files..."

# Check root directory
if [ -f "/var/www/html/enhanced_cart.php" ]; then
    echo "Found: /var/www/html/enhanced_cart.php"
    ROOT_CART_SIZE=$(stat -c '%s' /var/www/html/enhanced_cart.php 2>/dev/null || echo "0")
    echo "  Size: $ROOT_CART_SIZE bytes"
    
    # Check if it's a redirect file
    if grep -q "header.*Location" /var/www/html/enhanced_cart.php 2>/dev/null; then
        log_success "Root cart file is redirect (correct)"
    else
        log_issue "Root cart file may contain conflicting logic" "warning"
    fi
fi

# Check MLM directory
if [ -f "/var/www/html/mlm/enhanced_cart.php" ]; then
    echo "Found: /var/www/html/mlm/enhanced_cart.php"
    MLM_CART_SIZE=$(stat -c '%s' /var/www/html/mlm/enhanced_cart.php 2>/dev/null || echo "0")
    echo "  Size: $MLM_CART_SIZE bytes"
    log_success "MLM cart file exists"
else
    log_issue "MLM cart file missing" "critical"
fi

# Test 2.2: Check for duplicate checkout files
echo -e "\nTEST 2.2: Duplicate Checkout Files Check"

if [ -f "/var/www/html/checkout_confirmation.php" ]; then
    log_issue "Duplicate checkout file in root directory" "warning"
fi

if [ -f "/var/www/html/mlm/checkout_confirmation.php" ]; then
    log_success "MLM checkout file exists"
    CHECKOUT_SIZE=$(stat -c '%s' /var/www/html/mlm/checkout_confirmation.php 2>/dev/null || echo "0")
    echo "  Size: $CHECKOUT_SIZE bytes"
else
    log_issue "MLM checkout file missing" "critical"
fi

# Test 2.3: Session Management Analysis
echo -e "\nTEST 2.3: Session Management Analysis"

# Check cart session handling
if echo "$CART_CONTENT" | grep -q "session_start"; then
    log_success "Cart has session_start()"
else
    log_issue "Cart missing session_start()" "critical"
fi

# Check checkout session handling
if echo "$CHECKOUT_CONTENT" | grep -q "session_start"; then
    log_success "Checkout has session_start()"
else
    log_issue "Checkout missing session_start()" "critical"
fi

# Check for session cart variables
if echo "$CART_CONTENT" | grep -q "\$_SESSION\['cart'\]"; then
    log_success "Cart uses session cart variable"
else
    log_issue "Cart not using session cart variable" "critical"
fi

if echo "$CHECKOUT_CONTENT" | grep -q "\$_SESSION\['cart'\]"; then
    log_success "Checkout accesses session cart variable"
else
    log_issue "Checkout not accessing session cart variable" "critical"
fi

echo ""

# SECTION 3: RECENT FIXES VERIFICATION
echo -e "${PURPLE}📋 SECTION 3: RECENT FIXES VERIFICATION${NC}"
echo "======================================="

# Test 3.1: Cart Persistence Logic
echo "TEST 3.1: Cart Persistence Logic Verification"

if echo "$CHECKOUT_CONTENT" | grep -q "last_order_items"; then
    log_success "Cart persistence logic (last_order_items) implemented"
else
    log_issue "Cart persistence logic missing" "critical"
fi

if echo "$CHECKOUT_CONTENT" | grep -q "last_order_total"; then
    log_success "Order total persistence implemented"
else
    log_issue "Order total persistence missing" "warning"
fi

# Test 3.2: GCash Payment Option
echo -e "\nTEST 3.2: GCash Payment Option Verification"

if echo "$CHECKOUT_CONTENT" | grep -q "GCash Payment"; then
    log_success "GCash payment option label found"
else
    log_issue "GCash payment option label missing" "warning"
fi

if echo "$CHECKOUT_CONTENT" | grep -q "***********"; then
    log_success "GCash phone number found"
else
    log_issue "GCash phone number missing" "warning"
fi

if echo "$CHECKOUT_CONTENT" | grep -q "Evelyn Percil"; then
    log_success "GCash account name found"
else
    log_issue "GCash account name missing" "warning"
fi

# Test 3.3: File Permissions
echo -e "\nTEST 3.3: File Permissions Verification"

# Check cart file permissions
if [ -f "/var/www/html/mlm/enhanced_cart.php" ]; then
    CART_PERMS=$(stat -c '%a' /var/www/html/mlm/enhanced_cart.php 2>/dev/null)
    CART_OWNER=$(stat -c '%U:%G' /var/www/html/mlm/enhanced_cart.php 2>/dev/null)
    
    if [ "$CART_PERMS" = "644" ]; then
        log_success "Cart file permissions correct (644)"
    else
        log_issue "Cart file permissions incorrect ($CART_PERMS, should be 644)" "warning"
    fi
    
    if [ "$CART_OWNER" = "www-data:www-data" ]; then
        log_success "Cart file ownership correct (www-data:www-data)"
    else
        log_issue "Cart file ownership incorrect ($CART_OWNER, should be www-data:www-data)" "warning"
    fi
fi

# Check checkout file permissions
if [ -f "/var/www/html/mlm/checkout_confirmation.php" ]; then
    CHECKOUT_PERMS=$(stat -c '%a' /var/www/html/mlm/checkout_confirmation.php 2>/dev/null)
    CHECKOUT_OWNER=$(stat -c '%U:%G' /var/www/html/mlm/checkout_confirmation.php 2>/dev/null)
    
    if [ "$CHECKOUT_PERMS" = "644" ]; then
        log_success "Checkout file permissions correct (644)"
    else
        log_issue "Checkout file permissions incorrect ($CHECKOUT_PERMS, should be 644)" "warning"
    fi
    
    if [ "$CHECKOUT_OWNER" = "www-data:www-data" ]; then
        log_success "Checkout file ownership correct (www-data:www-data)"
    else
        log_issue "Checkout file ownership incorrect ($CHECKOUT_OWNER, should be www-data:www-data)" "warning"
    fi
fi

echo ""

# SECTION 4: SPECIFIC DIAGNOSTIC INFORMATION
echo -e "${PURPLE}📋 SECTION 4: SPECIFIC DIAGNOSTIC INFORMATION${NC}"
echo "=============================================="

# Test 4.1: Session Configuration
echo "TEST 4.1: Session Configuration Analysis"

SESSION_PATH=$(php -r 'echo session_save_path();' 2>/dev/null || echo "unknown")
echo "Session save path: $SESSION_PATH"

if [ -d "$SESSION_PATH" ] && [ -w "$SESSION_PATH" ]; then
    log_success "Session directory writable"
else
    log_issue "Session directory not writable" "critical"
fi

# Test 4.2: Apache Error Logs
echo -e "\nTEST 4.2: Apache Error Log Analysis"
echo "Recent Apache errors related to cart/checkout:"

RECENT_ERRORS=$(sudo tail -50 /var/log/apache2/error.log 2>/dev/null | grep -i "cart\|checkout\|mlm" | tail -5)
if [ -n "$RECENT_ERRORS" ]; then
    echo "$RECENT_ERRORS"
    log_issue "Recent Apache errors found" "warning"
else
    log_success "No recent Apache errors related to cart/checkout"
fi

# Test 4.3: Database Connectivity
echo -e "\nTEST 4.3: Database Connectivity Test"

DB_TEST=$(php -r "
try {
    \$pdo = new PDO('mysql:host=localhost;dbname=drupal_umd', 'drupal_user', 'secure_drupal_pass_1748318545');
    echo 'Database connection successful';
} catch (PDOException \$e) {
    echo 'Database connection failed: ' . \$e->getMessage();
}
" 2>/dev/null)

if echo "$DB_TEST" | grep -q "successful"; then
    log_success "Database connectivity working"
else
    log_issue "Database connectivity issues: $DB_TEST" "critical"
fi

echo ""

# SECTION 5: SUMMARY AND RECOMMENDATIONS
echo -e "${PURPLE}📊 COMPREHENSIVE REVIEW SUMMARY${NC}"
echo "================================"
echo "Total Issues Found: $ISSUES_FOUND"
echo "Critical Issues: ${#CRITICAL_ISSUES[@]}"
echo "Warnings: ${#WARNINGS[@]}"
echo ""

if [ ${#CRITICAL_ISSUES[@]} -gt 0 ]; then
    echo -e "${RED}🚨 CRITICAL ISSUES REQUIRING IMMEDIATE ATTENTION:${NC}"
    for issue in "${CRITICAL_ISSUES[@]}"; do
        echo -e "${RED}  • $issue${NC}"
    done
    echo ""
fi

if [ ${#WARNINGS[@]} -gt 0 ]; then
    echo -e "${YELLOW}⚠️  WARNINGS REQUIRING ATTENTION:${NC}"
    for warning in "${WARNINGS[@]}"; do
        echo -e "${YELLOW}  • $warning${NC}"
    done
    echo ""
fi

# Provide specific recommendations
echo -e "${BLUE}🔧 IMMEDIATE CORRECTIVE ACTIONS:${NC}"

if [ ${#CRITICAL_ISSUES[@]} -eq 0 ]; then
    echo -e "${GREEN}✅ No critical issues found - system appears functional${NC}"
    echo -e "${BLUE}📋 Recommended manual testing:${NC}"
    echo "1. Visit: https://extremelifeherbal.com/enhanced_cart.php"
    echo "2. Add test product and proceed to checkout"
    echo "3. Complete order and verify cart persistence"
else
    echo -e "${RED}🚨 Critical issues detected - immediate fixes required${NC}"
    echo "1. Fix critical issues listed above"
    echo "2. Restart Apache after fixes"
    echo "3. Re-run this diagnostic script"
    echo "4. Perform manual testing"
fi

echo ""
echo -e "${BLUE}📍 TESTING URLS:${NC}"
echo "Cart: https://extremelifeherbal.com/enhanced_cart.php"
echo "MLM Cart: https://extremelifeherbal.com/mlm/enhanced_cart.php"
echo "Checkout: https://extremelifeherbal.com/mlm/checkout_confirmation.php"

echo ""
echo -e "${GREEN}🎯 REVIEW COMPLETED${NC}"
echo "==================="
echo "Date: $(date)"
echo "Server: *************** (extremelifeherbal.com)"
echo "Focus: Persistent 'cart empty after complete order' issue"
