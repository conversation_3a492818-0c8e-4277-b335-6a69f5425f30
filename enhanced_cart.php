<?php
// ExtremeLife MLM Advanced Shopping Cart with Enhanced Features v2.0
session_start();

// Load sponsor system data
$sponsor_data = [];
if (file_exists('sponsor_system_data.json')) {
    $sponsor_data = json_decode(file_get_contents('sponsor_system_data.json'), true);
}

// Database connection with enhanced error handling
try {
    $pdo = new PDO('mysql:host=localhost;dbname=drupal_umd', 'drupal_user', 'secure_drupal_pass_1748318545');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $db_available = true;
} catch (PDOException $e) {
    $db_available = false;
    error_log("Cart DB connection failed: " . $e->getMessage());
}

// Initialize advanced cart with wishlist and comparison
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}
if (!isset($_SESSION['wishlist'])) {
    $_SESSION['wishlist'] = [];
}
if (!isset($_SESSION['recently_viewed'])) {
    $_SESSION['recently_viewed'] = [];
}

// Get member information with enhanced data
$member_id = $_SESSION['member_id'] ?? 10;
$member = null;

if ($db_available) {
    try {
        $stmt = $pdo->prepare("SELECT m.*, CONCAT(m.first_name, ' ', m.last_name) as user_name,
                              g.name as group_name, g.commission_rate, g.rebate_rate, g.sales_threshold,
                              COALESCE(SUM(o.total_amount), 0) as total_purchases,
                              COUNT(o.id) as total_orders
                              FROM mlm_members m
                              LEFT JOIN mlm_user_groups g ON m.user_group_id = g.id
                              LEFT JOIN mlm_orders o ON m.id = o.member_id AND o.status = 'completed'
                              WHERE m.id = ? GROUP BY m.id");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();
    } catch (PDOException $e) {
        // Fallback handled below
    }
}

// Enhanced fallback member data
if (!$member) {
    $member = [
        'id' => 10,
        'user_name' => 'Demo Member',
        'email' => '<EMAIL>',
        'referral_code' => 'ELH000010',
        'group_name' => 'Member',
        'commission_rate' => 10.00,
        'rebate_rate' => 2.00,
        'sales_threshold' => 1000.00,
        'total_purchases' => 2450.00,
        'total_orders' => 8,
        'phone' => '+63 ************'
    ];
}

// Function to get member-specific pricing
function getMemberPrice($product, $group_name) {
    switch (strtolower($group_name)) {
        case 'member':
            return $product['price'];
        case 'wholesale':
            return $product['wholesale_price'];
        case 'distributor':
            return isset($product['distributor_price']) ? $product['distributor_price'] : $product['wholesale_price'] * 0.9;
        case 'vip':
            return isset($product['vip_price']) ? $product['vip_price'] : $product['wholesale_price'] * 0.85;
        case 'diamond':
            return isset($product['diamond_price']) ? $product['diamond_price'] : $product['wholesale_price'] * 0.8;
        default:
            return $product['price'];
    }
}

// Add getMemberInfo function for e-commerce integration
function getMemberInfo($member_id) {
    // Include database configuration
    $config_file = dirname(__DIR__) . '/config/database.php';
    if (file_exists($config_file)) {
        $config = include $config_file;
    } else {
        return [
            'id' => $member_id,
            'first_name' => 'Member',
            'last_name' => '',
            'user_group_id' => 1,
            'group_name' => 'Member',
            'group_commission_rate' => 10
        ];
    }

    try {
        $pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("
            SELECT m.*, ug.group_name, ug.commission_rate as group_commission_rate
            FROM mlm_members m
            LEFT JOIN mlm_user_groups ug ON m.user_group_id = ug.id
            WHERE m.id = ?
        ");
        $stmt->execute([$member_id]);
        $result = $stmt->fetch();

        if (!$result) {
            return [
                'id' => $member_id,
                'first_name' => 'Member',
                'last_name' => '',
                'user_group_id' => 1,
                'group_name' => 'Member',
                'group_commission_rate' => 10
            ];
        }

        return $result;
    } catch (PDOException $e) {
        error_log("Error getting member info: " . $e->getMessage());
        return [
            'id' => $member_id,
            'first_name' => 'Member',
            'last_name' => '',
            'user_group_id' => 1,
            'group_name' => 'Member',
            'group_commission_rate' => 10
        ];
    }
}

// Load products from database
$products = [];
if ($db_available) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM mlm_products WHERE status = 'active' ORDER BY name");
        $stmt->execute();
        $db_products = $stmt->fetchAll();

        // Convert to associative array with ID as key for compatibility
        foreach ($db_products as $product) {
            $products[$product['id']] = [
                'id' => $product['id'],
                'name' => $product['name'],
                'price' => $product['retail_price'],
                'wholesale_price' => $product['wholesale_price'] ?? ($product['retail_price'] * 0.75),
                'distributor_price' => $product['distributor_price'] ?? ($product['retail_price'] * 0.70),
                'vip_price' => $product['vip_price'] ?? ($product['retail_price'] * 0.65),
                'diamond_price' => $product['diamond_price'] ?? ($product['retail_price'] * 0.60),
                'image' => basename($product['image_url'] ?? 'default-product.jpg'),
                'description' => $product['description'] ?? 'Premium ExtremeLife herbal product',
                'category' => $product['category'] ?? 'Supplements',
                'weight' => $product['weight'] ?? 'Standard size',
                'supplier' => 'ExtremeLife Labs',
                'stock' => $product['inventory_count'] ?? 100,
                'rating' => 4.8, // Default rating
                'reviews' => rand(50, 200), // Random review count
                'featured' => $product['featured'] ?? true,
                'benefits' => ['Premium quality', 'Natural ingredients', 'MLM approved'],
                'rebate_rate' => $product['rebate_rate'] ?? 3.0
            ];
        }
    } catch (PDOException $e) {
        error_log("Error loading products: " . $e->getMessage());
    }
}

// Fallback products if database is not available
if (empty($products)) {
    $products = [
        1 => [
            'id' => 1, 'name' => 'Premium Turmeric Supplement', 'price' => 299.99, 'wholesale_price' => 199.99,
            'image' => 'turmeric.jpg', 'description' => 'Natural anti-inflammatory supplement with 95% curcumin',
            'category' => 'Supplements', 'weight' => '60 capsules', 'supplier' => 'ExtremeLife Labs',
            'stock' => 150, 'rating' => 4.8, 'reviews' => 124, 'featured' => true,
            'benefits' => ['Anti-inflammatory', 'Joint health', 'Antioxidant'], 'rebate_rate' => 3.0
        ],
        2 => [
            'id' => 2, 'name' => 'Organic Green Tea Blend', 'price' => 249.99, 'wholesale_price' => 169.99,
            'image' => 'green-tea.jpg', 'description' => 'Antioxidant-rich organic green tea blend',
            'category' => 'Herbal Teas', 'weight' => '100g loose leaf', 'supplier' => 'ExtremeLife Labs',
            'stock' => 200, 'rating' => 4.7, 'reviews' => 89, 'featured' => true,
            'benefits' => ['Antioxidants', 'Weight management', 'Energy boost'], 'rebate_rate' => 2.5
        ]
    ];
}

// Handle enhanced cart actions
$message = '';
$error = '';
$success_type = '';

if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'add_to_cart':
            $product_id = (int)$_POST['product_id'];
            $quantity = (int)$_POST['quantity'];

            if (isset($products[$product_id]) && $quantity > 0) {
                // Check stock availability
                if ($quantity > $products[$product_id]['stock']) {
                    $error = "Only {$products[$product_id]['stock']} items available in stock.";
                } else {
                    if (isset($_SESSION['cart'][$product_id])) {
                        $_SESSION['cart'][$product_id] += $quantity;
                    } else {
                        $_SESSION['cart'][$product_id] = $quantity;
                    }

                    // Add to recently viewed
                    $_SESSION['recently_viewed'] = array_unique(array_merge([$product_id], $_SESSION['recently_viewed']));
                    $_SESSION['recently_viewed'] = array_slice($_SESSION['recently_viewed'], 0, 5);

                    $message = "✅ {$products[$product_id]['name']} added to cart successfully!";
                    $success_type = 'add_cart';
                }
            } else {
                $error = "Invalid product or quantity.";
            }
            break;

        case 'add_to_wishlist':
            $product_id = (int)$_POST['product_id'];
            if (isset($products[$product_id])) {
                if (!in_array($product_id, $_SESSION['wishlist'])) {
                    $_SESSION['wishlist'][] = $product_id;
                    $message = "💖 {$products[$product_id]['name']} added to wishlist!";
                    $success_type = 'wishlist';
                } else {
                    $message = "Product is already in your wishlist.";
                    $success_type = 'info';
                }
            }
            break;

        case 'remove_from_wishlist':
            $product_id = (int)$_POST['product_id'];
            $_SESSION['wishlist'] = array_filter($_SESSION['wishlist'], function($id) use ($product_id) {
                return $id != $product_id;
            });
            $message = "Product removed from wishlist.";
            $success_type = 'info';
            break;

        case 'move_to_cart':
            $product_id = (int)$_POST['product_id'];
            if (in_array($product_id, $_SESSION['wishlist'])) {
                $_SESSION['cart'][$product_id] = $_SESSION['cart'][$product_id] ?? 0;
                $_SESSION['cart'][$product_id] += 1;
                $_SESSION['wishlist'] = array_filter($_SESSION['wishlist'], function($id) use ($product_id) {
                    return $id != $product_id;
                });
                $message = "🛒 Product moved from wishlist to cart!";
                $success_type = 'move_cart';
            }
            break;

        case 'update_cart':
            $updated_items = 0;
            foreach ($_POST['quantities'] as $product_id => $quantity) {
                $quantity = (int)$quantity;
                if ($quantity > 0) {
                    if ($quantity <= $products[$product_id]['stock']) {
                        $_SESSION['cart'][$product_id] = $quantity;
                        $updated_items++;
                    } else {
                        $error = "Stock limit exceeded for {$products[$product_id]['name']}";
                    }
                } else {
                    unset($_SESSION['cart'][$product_id]);
                    $updated_items++;
                }
            }
            if (!$error) {
                $message = "🔄 Cart updated successfully! ($updated_items items)";
                $success_type = 'update';
            }
            break;

        case 'remove_item':
            $product_id = (int)$_POST['product_id'];
            if (isset($_SESSION['cart'][$product_id])) {
                $product_name = $products[$product_id]['name'];
                unset($_SESSION['cart'][$product_id]);
                $message = "🗑️ $product_name removed from cart.";
                $success_type = 'remove';
            }
            break;

        case 'clear_cart':
            $item_count = count($_SESSION['cart']);
            $_SESSION['cart'] = [];
            $message = "🧹 Cart cleared! ($item_count items removed)";
            $success_type = 'clear';
            break;

        case 'apply_coupon':
            $coupon_code = strtoupper(trim($_POST['coupon_code'] ?? ''));
            $valid_coupons = [
                'WELCOME10' => ['discount' => 10, 'type' => 'percentage', 'min_amount' => 500],
                'SAVE50' => ['discount' => 50, 'type' => 'fixed', 'min_amount' => 200],
                'MEMBER15' => ['discount' => 15, 'type' => 'percentage', 'min_amount' => 1000]
            ];

            if (isset($valid_coupons[$coupon_code])) {
                $_SESSION['applied_coupon'] = $valid_coupons[$coupon_code];
                $_SESSION['applied_coupon']['code'] = $coupon_code;
                $message = "🎉 Coupon '$coupon_code' applied successfully!";
                $success_type = 'coupon';
            } else {
                $error = "Invalid coupon code.";
            }
            break;

        case 'remove_coupon':
            unset($_SESSION['applied_coupon']);
            $message = "Coupon removed.";
            $success_type = 'info';
            break;

        case 'checkout':
            if (empty($_SESSION['cart'])) {
                $error = "Your cart is empty.";
            } else {
                // Process checkout
                $order_total = 0;
                $order_items = [];

                foreach ($_SESSION['cart'] as $product_id => $quantity) {
                    if (isset($products[$product_id])) {
                        $product = $products[$product_id];
                        $subtotal = $product['price'] * $quantity;
                        $order_total += $subtotal;

                        $order_items[] = [
                            'product_id' => $product_id,
                            'name' => $product['name'],
                            'price' => $product['price'],
                            'quantity' => $quantity,
                            'subtotal' => $subtotal
                        ];
                    }
                }

                // Calculate commissions
                $commission_amount = $order_total * ($member['commission_rate'] / 100);
                $rebate_amount = $order_total * ($member['rebate_rate'] / 100);
                $total_earnings = $commission_amount + $rebate_amount;

                // Generate order number
                $order_number = 'ELH' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

                // Store order in session for confirmation
                $_SESSION['pending_order'] = [
                    'order_number' => $order_number,
                    'items' => $order_items,
                    'total' => $order_total,
                    'commission' => $commission_amount,
                    'rebate' => $rebate_amount,
                    'total_earnings' => $total_earnings,
                    'member_id' => $member['id'],
                    'member_name' => $member['user_name'],
                    'created_at' => date('Y-m-d H:i:s')
                ];

                // Redirect to checkout confirmation
                header('Location: checkout_confirmation.php');
                exit;
            }
            break;
    }
}

// Calculate enhanced cart totals with advanced features
$cart_subtotal = 0;
$cart_items = [];
$total_savings = 0;
$total_rebate = 0;
$cart_weight = 0;

foreach ($_SESSION['cart'] as $product_id => $quantity) {
    if (isset($products[$product_id])) {
        $product = $products[$product_id];

        // Use member pricing based on user group
        $price = getMemberPrice($product, $member['group_name'] ?? 'Member');
        $retail_price = $product['price'];
        $subtotal = $price * $quantity;
        $retail_subtotal = $retail_price * $quantity;

        // Calculate individual rebate
        $item_rebate = $subtotal * ($product['rebate_rate'] / 100);
        $total_rebate += $item_rebate;

        // Calculate savings from wholesale pricing
        $item_savings = ($retail_subtotal - $subtotal);
        $total_savings += $item_savings;

        $cart_subtotal += $subtotal;

        $cart_items[] = [
            'id' => $product_id,
            'name' => $product['name'],
            'price' => $price,
            'retail_price' => $retail_price,
            'quantity' => $quantity,
            'subtotal' => $subtotal,
            'retail_subtotal' => $retail_subtotal,
            'savings' => $item_savings,
            'rebate' => $item_rebate,
            'stock' => $product['stock'],
            'rating' => $product['rating'],
            'category' => $product['category'],
            'weight' => $product['weight'],
            'benefits' => $product['benefits']
        ];
    }
}

// Apply coupon discount
$coupon_discount = 0;
$cart_total = $cart_subtotal;

if (isset($_SESSION['applied_coupon'])) {
    $coupon = $_SESSION['applied_coupon'];
    if ($cart_subtotal >= $coupon['min_amount']) {
        if ($coupon['type'] === 'percentage') {
            $coupon_discount = $cart_subtotal * ($coupon['discount'] / 100);
        } else {
            $coupon_discount = $coupon['discount'];
        }
        $cart_total = $cart_subtotal - $coupon_discount;
    }
}

// Calculate enhanced MLM earnings
$potential_commission = $cart_total * ($member['commission_rate'] / 100);
$potential_rebate = $total_rebate; // Use product-specific rebates
$total_potential_earnings = $potential_commission + $potential_rebate;

// Calculate progress to next rank
$next_rank_progress = 0;
$next_rank_name = '';
$remaining_to_next_rank = 0;

$ranks = [
    ['name' => 'Member', 'threshold' => 0, 'commission' => 10],
    ['name' => 'Wholesale', 'threshold' => 1000, 'commission' => 12],
    ['name' => 'Distributor', 'threshold' => 5000, 'commission' => 15],
    ['name' => 'VIP', 'threshold' => 15000, 'commission' => 18],
    ['name' => 'Diamond', 'threshold' => 50000, 'commission' => 20]
];

$current_purchases = $member['total_purchases'] + $cart_total;
foreach ($ranks as $rank) {
    if ($current_purchases < $rank['threshold']) {
        $next_rank_name = $rank['name'];
        $remaining_to_next_rank = $rank['threshold'] - $current_purchases;
        $next_rank_progress = (($current_purchases - ($rank['threshold'] - $rank['threshold'])) / $rank['threshold']) * 100;
        break;
    }
}

// Get wishlist items
$wishlist_items = [];
foreach ($_SESSION['wishlist'] as $product_id) {
    if (isset($products[$product_id])) {
        $wishlist_items[] = $products[$product_id] + ['id' => $product_id];
    }
}

// Get recently viewed items
$recently_viewed_items = [];
foreach ($_SESSION['recently_viewed'] as $product_id) {
    if (isset($products[$product_id]) && !isset($_SESSION['cart'][$product_id])) {
        $recently_viewed_items[] = $products[$product_id] + ['id' => $product_id];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Shopping Cart - ExtremeLife Herbal MLM</title>
    <style>
        :root {
            --primary-color: #2d5a27;
            --secondary-color: #4a7c59;
            --accent-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --white: #ffffff;
            --text-dark: #343a40;
            --text-muted: #6c757d;
            --border-color: #dee2e6;
            --shadow: 0 8px 25px rgba(0,0,0,0.1);
            --gradient-bg: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        * {
            margin: 0; padding: 0; box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-bg); min-height: 100vh; line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--white); padding: 2.5rem 2rem; text-align: center;
            box-shadow: var(--shadow); position: relative;
        }

        .header h1 {
            font-size: 3rem; margin-bottom: 0.5rem; font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p { font-size: 1.2rem; opacity: 0.95; }

        .member-badge {
            position: absolute; top: 1rem; right: 1rem;
            background: rgba(255,255,255,0.2); padding: 0.5rem 1rem;
            border-radius: 20px; font-size: 0.9rem;
        }

        .nav {
            background: var(--white); padding: 1.5rem; text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem;
            position: sticky; top: 0; z-index: 100;
        }

        .nav a {
            color: var(--primary-color); text-decoration: none; margin: 0 20px;
            padding: 12px 24px; border-radius: 30px; font-weight: 600;
            transition: all 0.2s ease; display: inline-block;
        }

        .nav a:hover {
            background: #e8f5e8; transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(45, 90, 39, 0.2);
        }

        .container { max-width: 1400px; margin: 0 auto; padding: 2rem; }

        .alert {
            padding: 1.5rem; border-radius: 12px; margin-bottom: 2rem;
            font-weight: 600; display: flex; align-items: center;
            animation: slideInDown 0.5s ease; position: relative;
        }

        @keyframes slideInDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .alert:before {
            content: ''; position: absolute; top: 0; left: 0;
            width: 4px; height: 100%; background: currentColor;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724; border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24; border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460; border: 1px solid #bee5eb;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404; border: 1px solid #ffeaa7;
        }

        .cart-layout {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
            align-items: start;
        }

        .cart-section {
            background: var(--white); padding: 2.5rem; border-radius: 20px;
            box-shadow: var(--shadow); position: relative;
        }

        .cart-section:before {
            content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        .section-title {
            color: var(--primary-color); font-size: 1.8rem; margin-bottom: 2rem;
            border-bottom: 3px solid #e8f5e8; padding-bottom: 1rem; font-weight: 700;
        }

        .cart-item {
            display: grid;
            grid-template-columns: 80px 1fr 120px 100px 120px 80px;
            gap: 1.5rem; align-items: center; padding: 1.5rem 0;
            border-bottom: 2px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .cart-item:hover {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            transform: translateX(5px);
            border-radius: 10px; padding-left: 1rem; padding-right: 1rem;
        }

        .cart-item:last-child { border-bottom: none; }

        .product-image {
            width: 70px; height: 70px; object-fit: cover; border-radius: 12px;
            border: 3px solid var(--border-color); transition: all 0.2s ease;
        }

        .product-image:hover {
            transform: scale(1.1); border-color: var(--primary-color);
        }

        .product-info h4 {
            color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.1rem;
        }

        .product-info p {
            color: var(--text-muted); font-size: 0.9rem; margin-bottom: 0.3rem;
        }

        .product-benefits {
            display: flex; gap: 0.3rem; flex-wrap: wrap; margin-top: 0.5rem;
        }

        .benefit-tag {
            background: #e8f5e8; color: var(--primary-color);
            padding: 0.2rem 0.5rem; border-radius: 10px; font-size: 0.7rem;
        }

        .quantity-input {
            width: 70px; padding: 0.8rem; border: 2px solid var(--border-color);
            border-radius: 8px; text-align: center; font-weight: 600;
            transition: all 0.2s ease;
        }

        .quantity-input:focus {
            border-color: var(--primary-color); outline: none;
            box-shadow: 0 0 0 3px rgba(45, 90, 39, 0.1);
        }

        .btn {
            background: var(--primary-color); color: var(--white);
            padding: 0.8rem 1.8rem; border: none; border-radius: 30px;
            cursor: pointer; font-weight: 600; text-decoration: none;
            display: inline-block; margin: 0.3rem; transition: all 0.2s ease;
            text-transform: uppercase; letter-spacing: 0.5px;
        }

        .btn:hover {
            background: var(--secondary-color); transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(45, 90, 39, 0.3);
        }

        .btn-sm { padding: 0.4rem 0.8rem; font-size: 0.8rem; }
        .btn-danger { background: var(--danger-color); }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: var(--accent-color); }
        .btn-success:hover { background: #218838; }
        .btn-warning { background: var(--warning-color); color: var(--text-dark); }
        .btn-warning:hover { background: #e0a800; }
        .btn-info { background: var(--info-color); }
        .btn-info:hover { background: #138496; }

        .earnings-card {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 2rem; border-radius: 15px; margin-bottom: 2rem;
            border: 3px solid var(--accent-color); position: relative;
        }

        .earnings-card:before {
            content: '💰'; position: absolute; top: 1rem; right: 1rem;
            font-size: 2rem; opacity: 0.3;
        }

        .earnings-item {
            display: flex; justify-content: space-between; margin-bottom: 0.8rem;
            font-size: 1.1rem;
        }

        .earnings-total {
            border-top: 2px solid var(--accent-color); padding-top: 1rem;
            margin-top: 1rem; font-size: 1.3rem; font-weight: 700;
        }

        .rank-progress {
            background: var(--light-bg); padding: 1.5rem; border-radius: 12px;
            margin-bottom: 1.5rem; border-left: 4px solid var(--info-color);
        }

        .progress-bar {
            width: 100%; height: 12px; background: var(--border-color);
            border-radius: 6px; overflow: hidden; margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%; background: linear-gradient(90deg, var(--info-color), var(--accent-color));
            border-radius: 6px; transition: width 1s ease;
        }

        .coupon-section {
            background: #fff3cd; padding: 1.5rem; border-radius: 12px;
            border: 2px solid var(--warning-color); margin-bottom: 1.5rem;
        }

        .coupon-input {
            display: flex; gap: 0.5rem; margin-top: 1rem;
        }

        .coupon-input input {
            flex: 1; padding: 0.8rem; border: 2px solid var(--border-color);
            border-radius: 8px; text-transform: uppercase;
        }

        .total-section {
            background: var(--light-bg); padding: 2rem; border-radius: 15px;
            border-left: 4px solid var(--primary-color); margin-top: 1.5rem;
        }

        .total-row {
            display: flex; justify-content: space-between; margin-bottom: 0.8rem;
            font-size: 1.1rem;
        }

        .total-final {
            border-top: 2px solid var(--primary-color); padding-top: 1rem;
            margin-top: 1rem; font-size: 1.4rem; font-weight: 700;
            color: var(--primary-color);
        }

        .sidebar-section {
            background: var(--white); padding: 1.5rem; border-radius: 15px;
            box-shadow: var(--shadow); margin-bottom: 1.5rem;
        }

        .wishlist-item, .recent-item {
            display: flex; align-items: center; gap: 1rem; padding: 1rem;
            border-bottom: 1px solid var(--border-color); transition: all 0.2s ease;
        }

        .wishlist-item:hover, .recent-item:hover {
            background: var(--light-bg); border-radius: 8px;
        }

        .item-image {
            width: 50px; height: 50px; object-fit: cover; border-radius: 8px;
        }

        .item-info h5 {
            color: var(--primary-color); margin-bottom: 0.3rem; font-size: 0.9rem;
        }

        .item-price {
            color: var(--accent-color); font-weight: 700;
        }

        @media (max-width: 1024px) {
            .cart-layout { grid-template-columns: 1fr; }
            .cart-item {
                grid-template-columns: 1fr;
                text-align: center; gap: 1rem;
            }
            .container { padding: 1rem; }
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .nav a { margin: 0.5rem; padding: 8px 16px; }
            .cart-item { padding: 1rem; }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="member-badge">
            <div style="font-size: 0.8rem; opacity: 0.9;">Current Rank</div>
            <div style="font-weight: 700; font-size: 1rem;"><?= htmlspecialchars($member['group_name'] ?? 'Member') ?></div>
        </div>

        <div style="display: flex; align-items: center; justify-content: center; gap: 2rem; flex-wrap: wrap;">
            <div style="text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 0.5rem;">🛒</div>
                <h1 style="font-size: 2.5rem; margin: 0;">ExtremeLife Shopping Cart</h1>
            </div>

            <div style="background: rgba(255,255,255,0.15); padding: 1.5rem; border-radius: 15px; text-align: center; min-width: 200px;">
                <div style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Welcome Back</div>
                <div style="font-size: 1.3rem; font-weight: 700; margin-bottom: 0.3rem;">
                    <?= htmlspecialchars($member['user_name']) ?>
                </div>
                <div style="font-size: 0.9rem; opacity: 0.8;">
                    ID: <?= htmlspecialchars($member['referral_code'] ?? 'ELH000010') ?>
                </div>
                <div style="font-size: 0.8rem; margin-top: 0.5rem; opacity: 0.9;">
                    📊 <?= $member['total_orders'] ?? 0 ?> Orders | ₱<?= number_format($member['total_purchases'] ?? 0, 2) ?> Total
                </div>
            </div>
        </div>

        <?php if ($next_rank_name): ?>
        <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px; margin-top: 1rem; text-align: center;">
            <div style="font-size: 0.9rem; margin-bottom: 0.5rem;">🎯 Progress to <?= $next_rank_name ?></div>
            <div style="background: rgba(255,255,255,0.2); border-radius: 10px; height: 8px; overflow: hidden;">
                <div style="background: #ffc107; height: 100%; width: <?= min(100, ($member['total_purchases'] / $remaining_to_next_rank) * 100) ?>%; transition: width 1s ease;"></div>
            </div>
            <div style="font-size: 0.8rem; margin-top: 0.3rem; opacity: 0.9;">
                ₱<?= number_format($remaining_to_next_rank, 2) ?> remaining
            </div>
        </div>
        <?php endif; ?>
    </header>

    <nav class="nav">
        <div style="display: flex; align-items: center; justify-content: center; gap: 1rem; flex-wrap: wrap;">
            <a href="/">🏠 Home</a>
            <a href="/database_catalog.php">📦 Shop Products</a>
            <a href="/member_dashboard.php">👤 Dashboard</a>
            <a href="/genealogy_tree.php">🌳 Genealogy</a>
            <a href="/register.php">👥 Refer Friends</a>
            <a href="/mlm_tools.php">🔧 MLM Tools</a>

            <div style="display: flex; gap: 1rem; margin-left: 2rem;">
                <div style="background: var(--accent-color); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; font-weight: 600;">
                    🛒 Cart: <?= count($_SESSION['cart']) ?> items
                </div>

                <?php if (!empty($_SESSION['wishlist'])): ?>
                <div style="background: var(--warning-color); color: var(--text-dark); padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; font-weight: 600;">
                    💖 Wishlist: <?= count($_SESSION['wishlist']) ?>
                </div>
                <?php endif; ?>

                <div style="background: var(--info-color); color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; font-weight: 600;">
                    💰 <?= $member['commission_rate'] ?? 10 ?>% Commission
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-<?= $success_type === 'add_cart' ? 'success' : ($success_type === 'wishlist' ? 'info' : ($success_type === 'coupon' ? 'warning' : 'success')) ?>">
                <?= $message ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">❌ <?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <div class="cart-layout">
            <!-- Main Cart Section -->
            <div class="cart-section">
                <h2 class="section-title">🛒 Your Shopping Cart</h2>

                <?php if (empty($cart_items)): ?>
                    <div style="text-align: center; padding: 4rem; color: var(--text-muted);">
                        <div style="font-size: 5rem; margin-bottom: 1.5rem; opacity: 0.5;">🛒</div>
                        <h3 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1.8rem;">Your cart is empty</h3>
                        <p style="font-size: 1.1rem; margin-bottom: 2rem;">Discover our premium herbal products and start earning MLM commissions!</p>
                        <a href="/database_catalog.php" class="btn btn-success" style="padding: 1rem 2rem; font-size: 1.1rem;">
                            🌿 Browse Products
                        </a>

                        <?php if (!empty($recently_viewed_items)): ?>
                        <div style="margin-top: 3rem;">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">Recently Viewed</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <?php foreach (array_slice($recently_viewed_items, 0, 3) as $item): ?>
                                <div style="background: var(--white); padding: 1rem; border-radius: 10px; border: 2px solid var(--border-color);">
                                    <img src="/images/products/<?= htmlspecialchars($item['image']) ?>"
                                         alt="<?= htmlspecialchars($item['name']) ?>"
                                         style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px; margin-bottom: 0.5rem;"
                                         onerror="this.src='/images/default-product.jpg'">
                                    <h5 style="color: var(--primary-color); margin-bottom: 0.5rem;"><?= htmlspecialchars($item['name']) ?></h5>
                                    <div style="color: var(--accent-color); font-weight: 700; margin-bottom: 0.5rem;">₱<?= number_format($item['price'], 2) ?></div>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="action" value="add_to_cart">
                                        <input type="hidden" name="product_id" value="<?= $item['id'] ?>">
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn btn-sm btn-success">Add to Cart</button>
                                    </form>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <form method="post" id="cartForm">
                        <input type="hidden" name="action" value="update_cart">

                        <!-- Cart Header -->
                        <div style="display: grid; grid-template-columns: 80px 1fr 120px 100px 120px 80px; gap: 1.5rem; padding: 1rem; background: var(--light-bg); border-radius: 10px; font-weight: 700; color: var(--primary-color); margin-bottom: 1rem;">
                            <div>Image</div>
                            <div>Product Details</div>
                            <div>Price</div>
                            <div>Quantity</div>
                            <div>Subtotal</div>
                            <div>Actions</div>
                        </div>

                        <!-- Cart Items -->
                        <?php foreach ($cart_items as $item): ?>
                            <div class="cart-item" data-product-id="<?= $item['id'] ?>">
                                <!-- Product Image -->
                                <div>
                                    <img src="/images/products/<?= htmlspecialchars($products[$item['id']]['image']) ?>"
                                         alt="<?= htmlspecialchars($item['name']) ?>"
                                         class="product-image"
                                         onerror="handleImageError(this)">
                                </div>

                                <!-- Product Info -->
                                <div class="product-info">
                                    <h4><?= htmlspecialchars($item['name']) ?></h4>
                                    <p style="margin-bottom: 0.5rem;"><?= htmlspecialchars($products[$item['id']]['description']) ?></p>

                                    <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 0.5rem;">
                                        <span style="background: #e8f5e8; color: var(--primary-color); padding: 0.2rem 0.6rem; border-radius: 12px; font-size: 0.8rem;">
                                            📦 <?= htmlspecialchars($item['category']) ?>
                                        </span>
                                        <span style="color: var(--text-muted); font-size: 0.9rem;">
                                            ⭐ <?= $item['rating'] ?>/5.0
                                        </span>
                                        <span style="color: var(--text-muted); font-size: 0.9rem;">
                                            📦 <?= $item['stock'] ?> in stock
                                        </span>
                                    </div>

                                    <div class="product-benefits">
                                        <?php foreach (array_slice($item['benefits'], 0, 3) as $benefit): ?>
                                            <span class="benefit-tag"><?= htmlspecialchars($benefit) ?></span>
                                        <?php endforeach; ?>
                                    </div>

                                    <?php if ($item['savings'] > 0): ?>
                                    <div style="color: var(--accent-color); font-weight: 600; font-size: 0.9rem; margin-top: 0.5rem;">
                                        💰 You save: ₱<?= number_format($item['savings'], 2) ?> (Member Price!)
                                    </div>
                                    <?php endif; ?>

                                    <div style="color: var(--info-color); font-weight: 600; font-size: 0.9rem; margin-top: 0.3rem;">
                                        🎯 Rebate: ₱<?= number_format($item['rebate'], 2) ?> (<?= $products[$item['id']]['rebate_rate'] ?>%)
                                    </div>
                                </div>

                                <!-- Price -->
                                <div style="text-align: center;">
                                    <?php if ($item['savings'] > 0): ?>
                                        <div style="text-decoration: line-through; color: var(--text-muted); font-size: 0.9rem;">
                                            ₱<?= number_format($item['retail_price'], 2) ?>
                                        </div>
                                    <?php endif; ?>
                                    <div style="font-weight: 700; color: var(--primary-color); font-size: 1.1rem;">
                                        ₱<?= number_format($item['price'], 2) ?>
                                    </div>
                                    <?php if ($item['savings'] > 0): ?>
                                        <div style="color: var(--accent-color); font-size: 0.8rem; font-weight: 600;">
                                            Member Price
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Quantity -->
                                <div style="text-align: center;">
                                    <input type="number"
                                           name="quantities[<?= $item['id'] ?>]"
                                           value="<?= $item['quantity'] ?>"
                                           min="0"
                                           max="<?= $item['stock'] ?>"
                                           class="quantity-input"
                                           data-price="<?= $item['price'] ?>"
                                           data-product-id="<?= $item['id'] ?>"
                                           onchange="updateItemSubtotal(this)">
                                    <div style="font-size: 0.8rem; color: var(--text-muted); margin-top: 0.3rem;">
                                        Max: <?= $item['stock'] ?>
                                    </div>
                                </div>

                                <!-- Subtotal -->
                                <div style="text-align: center;">
                                    <div style="font-weight: 700; color: var(--primary-color); font-size: 1.2rem;" id="subtotal-<?= $item['id'] ?>">
                                        ₱<?= number_format($item['subtotal'], 2) ?>
                                    </div>
                                    <?php if ($item['rebate'] > 0): ?>
                                        <div style="color: var(--info-color); font-size: 0.8rem; font-weight: 600;">
                                            +₱<?= number_format($item['rebate'], 2) ?> rebate
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Actions -->
                                <div style="text-align: center;">
                                    <form method="post" style="display: inline; margin-bottom: 0.5rem;">
                                        <input type="hidden" name="action" value="add_to_wishlist">
                                        <input type="hidden" name="product_id" value="<?= $item['id'] ?>">
                                        <button type="submit" class="btn btn-sm btn-warning" title="Add to Wishlist">💖</button>
                                    </form>
                                    <br>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="action" value="remove_item">
                                        <input type="hidden" name="product_id" value="<?= $item['id'] ?>">
                                        <button type="submit" class="btn btn-sm btn-danger" title="Remove Item" onclick="return confirm('Remove this item from cart?')">🗑️</button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <!-- Cart Actions -->
                        <div style="margin-top: 2rem; padding: 1.5rem; background: var(--light-bg); border-radius: 15px; text-align: center;">
                            <button type="submit" class="btn btn-success" style="margin-right: 1rem;">
                                🔄 Update Cart
                            </button>
                            <button type="submit" name="action" value="clear_cart" class="btn btn-danger" onclick="return confirm('Clear entire cart?')">
                                🧹 Clear Cart
                            </button>
                            <a href="/database_catalog.php" class="btn btn-info">
                                ➕ Continue Shopping
                            </a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>

            <!-- Sidebar: MLM Earnings & Order Summary -->
            <div style="display: flex; flex-direction: column; gap: 1.5rem;">

                <?php if (!empty($cart_items)): ?>
                    <!-- Advanced MLM Earnings Card -->
                    <div class="earnings-card">
                        <h3 style="color: var(--primary-color); margin-bottom: 1.5rem; font-size: 1.3rem;">🎯 Your MLM Earnings</h3>

                        <!-- Current Rank Info -->
                        <div style="background: rgba(255,255,255,0.7); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-size: 0.9rem; color: var(--text-muted);">Current Rank</div>
                                    <div style="font-weight: 700; color: var(--primary-color);"><?= htmlspecialchars($member['group_name']) ?></div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 0.9rem; color: var(--text-muted);">Commission Rate</div>
                                    <div style="font-weight: 700; color: var(--accent-color);"><?= $member['commission_rate'] ?>%</div>
                                </div>
                            </div>
                        </div>

                        <!-- Earnings Breakdown -->
                        <div class="earnings-item">
                            <span>Base Commission (<?= $member['commission_rate'] ?>%):</span>
                            <strong>₱<?= number_format($potential_commission, 2) ?></strong>
                        </div>
                        <div class="earnings-item">
                            <span>Product Rebates (Variable):</span>
                            <strong>₱<?= number_format($potential_rebate, 2) ?></strong>
                        </div>
                        <?php if ($total_savings > 0): ?>
                        <div class="earnings-item">
                            <span>Member Savings:</span>
                            <strong style="color: var(--accent-color);">₱<?= number_format($total_savings, 2) ?></strong>
                        </div>
                        <?php endif; ?>

                        <div class="earnings-total">
                            <div style="display: flex; justify-content: space-between;">
                                <span><strong>Total Earnings:</strong></span>
                                <strong style="color: var(--accent-color); font-size: 1.4rem;">₱<?= number_format($total_potential_earnings, 2) ?></strong>
                            </div>
                        </div>

                        <!-- Rank Progress -->
                        <?php if ($next_rank_name): ?>
                        <div class="rank-progress">
                            <h4 style="color: var(--info-color); margin-bottom: 0.5rem;">🚀 Rank Advancement</h4>
                            <div style="font-size: 0.9rem; margin-bottom: 0.5rem;">
                                Progress to <strong><?= $next_rank_name ?></strong>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: <?= min(100, (($member['total_purchases'] + $cart_total) / ($member['total_purchases'] + $remaining_to_next_rank)) * 100) ?>%;"></div>
                            </div>
                            <div style="font-size: 0.8rem; margin-top: 0.5rem; color: var(--text-muted);">
                                ₱<?= number_format($remaining_to_next_rank - $cart_total, 2) ?> more needed after this order
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Coupon Section -->
                    <div class="coupon-section">
                        <h4 style="color: #856404; margin-bottom: 1rem;">🎫 Coupon Codes</h4>

                        <?php if (isset($_SESSION['applied_coupon'])): ?>
                            <div style="background: var(--accent-color); color: white; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong><?= $_SESSION['applied_coupon']['code'] ?></strong> Applied!
                                        <div style="font-size: 0.9rem; opacity: 0.9;">
                                            <?= $_SESSION['applied_coupon']['type'] === 'percentage' ? $_SESSION['applied_coupon']['discount'] . '% off' : '₱' . number_format($_SESSION['applied_coupon']['discount'], 2) . ' off' ?>
                                        </div>
                                    </div>
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="action" value="remove_coupon">
                                        <button type="submit" class="btn btn-sm btn-danger">Remove</button>
                                    </form>
                                </div>
                            </div>
                        <?php else: ?>
                            <form method="post" class="coupon-input">
                                <input type="hidden" name="action" value="apply_coupon">
                                <input type="text" name="coupon_code" placeholder="Enter coupon code" maxlength="20">
                                <button type="submit" class="btn btn-warning btn-sm">Apply</button>
                            </form>

                            <div style="margin-top: 1rem; font-size: 0.8rem; color: var(--text-muted);">
                                <strong>Available Coupons:</strong><br>
                                • WELCOME10 - 10% off orders ₱500+<br>
                                • SAVE50 - ₱50 off orders ₱200+<br>
                                • MEMBER15 - 15% off orders ₱1000+
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Order Total Section -->
                    <div class="total-section">
                        <h4 style="color: var(--primary-color); margin-bottom: 1rem;">📋 Order Summary</h4>

                        <div class="total-row">
                            <span>Subtotal (<?= count($cart_items) ?> items):</span>
                            <span>₱<?= number_format($cart_subtotal, 2) ?></span>
                        </div>

                        <?php if ($total_savings > 0): ?>
                        <div class="total-row" style="color: var(--accent-color);">
                            <span>Member Savings:</span>
                            <span>-₱<?= number_format($total_savings, 2) ?></span>
                        </div>
                        <?php endif; ?>

                        <?php if ($coupon_discount > 0): ?>
                        <div class="total-row" style="color: var(--warning-color);">
                            <span>Coupon Discount:</span>
                            <span>-₱<?= number_format($coupon_discount, 2) ?></span>
                        </div>
                        <?php endif; ?>

                        <div class="total-row">
                            <span>Shipping:</span>
                            <span style="color: var(--accent-color);">Free (Store Pickup)</span>
                        </div>

                        <div class="total-final">
                            <div style="display: flex; justify-content: space-between;">
                                <span>Total:</span>
                                <span>₱<?= number_format($cart_total, 2) ?></span>
                            </div>
                        </div>

                        <!-- Earnings Summary -->
                        <div style="background: #e8f5e8; padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                            <div style="font-size: 0.9rem; color: var(--primary-color); text-align: center;">
                                <strong>💰 You'll earn ₱<?= number_format($total_potential_earnings, 2) ?> from this order!</strong>
                            </div>
                        </div>
                    </div>

                    <!-- Checkout Button -->
                    <form method="post">
                        <input type="hidden" name="action" value="checkout">
                        <button type="submit" class="btn btn-success" style="width: 100%; padding: 1.2rem; font-size: 1.2rem; font-weight: 700;">
                            🛒 Proceed to Checkout
                        </button>
                    </form>

                    <!-- Pickup Information -->
                    <div style="background: #fff3cd; padding: 1.5rem; border-radius: 12px; border: 2px solid var(--warning-color);">
                        <h5 style="color: #856404; margin-bottom: 1rem;">📍 Store Pickup Information</h5>
                        <div style="font-size: 0.9rem; color: #856404;">
                            <strong>💡 Payment:</strong> Cash on pickup<br>
                            <strong>📍 Location:</strong> ExtremeLife Herbal Store<br>
                            <strong>⏰ Hours:</strong> Mon-Sat 9AM-6PM<br>
                            <strong>📞 Contact:</strong> +63 ************
                        </div>
                    </div>

                <?php endif; ?>

                <!-- Wishlist Section -->
                <?php if (!empty($wishlist_items)): ?>
                <div class="sidebar-section">
                    <h4 style="color: var(--primary-color); margin-bottom: 1rem;">💖 Your Wishlist</h4>

                    <?php foreach (array_slice($wishlist_items, 0, 3) as $item): ?>
                    <div class="wishlist-item">
                        <img src="/images/products/<?= htmlspecialchars($item['image']) ?>"
                             alt="<?= htmlspecialchars($item['name']) ?>"
                             class="item-image"
                             onerror="this.src='/images/default-product.jpg'">
                        <div class="item-info">
                            <h5><?= htmlspecialchars($item['name']) ?></h5>
                            <div class="item-price">₱<?= number_format($item['price'], 2) ?></div>
                            <div style="margin-top: 0.5rem;">
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="action" value="move_to_cart">
                                    <input type="hidden" name="product_id" value="<?= $item['id'] ?>">
                                    <button type="submit" class="btn btn-sm btn-success">Add to Cart</button>
                                </form>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="action" value="remove_from_wishlist">
                                    <input type="hidden" name="product_id" value="<?= $item['id'] ?>">
                                    <button type="submit" class="btn btn-sm btn-danger">Remove</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <?php if (count($wishlist_items) > 3): ?>
                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="/wishlist.php" class="btn btn-sm btn-info">View All (<?= count($wishlist_items) ?>)</a>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Recently Viewed Section -->
                <?php if (!empty($recently_viewed_items) && !empty($cart_items)): ?>
                <div class="sidebar-section">
                    <h4 style="color: var(--primary-color); margin-bottom: 1rem;">👁️ Recently Viewed</h4>

                    <?php foreach (array_slice($recently_viewed_items, 0, 2) as $item): ?>
                    <div class="recent-item">
                        <img src="/images/products/<?= htmlspecialchars($item['image']) ?>"
                             alt="<?= htmlspecialchars($item['name']) ?>"
                             class="item-image"
                             onerror="this.src='/images/default-product.jpg'">
                        <div class="item-info">
                            <h5><?= htmlspecialchars($item['name']) ?></h5>
                            <div class="item-price">₱<?= number_format($item['price'], 2) ?></div>
                            <form method="post" style="margin-top: 0.5rem;">
                                <input type="hidden" name="action" value="add_to_cart">
                                <input type="hidden" name="product_id" value="<?= $item['id'] ?>">
                                <input type="hidden" name="quantity" value="1">
                                <button type="submit" class="btn btn-sm btn-success">Add to Cart</button>
                            </form>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <!-- Empty Cart State -->
                <?php if (empty($cart_items)): ?>
                <div class="sidebar-section">
                    <div style="text-align: center; padding: 2rem; color: var(--text-muted);">
                        <div style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;">💰</div>
                        <h4 style="color: var(--primary-color); margin-bottom: 1rem;">Start Earning Today!</h4>
                        <p style="margin-bottom: 1.5rem;">Add products to your cart and see your potential MLM earnings in real-time.</p>
                        <div style="background: #e8f5e8; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                            <strong>Your Current Rate: <?= $member['commission_rate'] ?>%</strong><br>
                            <small>Plus product-specific rebates up to 6%!</small>
                        </div>
                        <a href="/database_catalog.php" class="btn btn-success">🌿 Browse Products</a>
                    </div>
                </div>
                <?php endif; ?>

            </div>
        </div>
    </div>

    <script>
        // ADVANCED EXTREMELIFE MLM CART JAVASCRIPT v2.0

        document.addEventListener('DOMContentLoaded', function() {
            initializeAdvancedCart();
            setupImageErrorHandling();
            setupQuantityValidation();
            setupFormEnhancements();
            setupAnimations();
            console.log('🚀 ExtremeLife Advanced Cart System Initialized');
        });

        function initializeAdvancedCart() {
            // Enhanced quantity input handling
            document.querySelectorAll('.quantity-input').forEach(input => {
                input.addEventListener('change', function() {
                    validateQuantity(this);
                    updateItemSubtotal(this);
                    updateCartTotals();
                });

                input.addEventListener('input', function() {
                    validateQuantity(this);
                });

                // Add +/- buttons for better UX
                addQuantityControls(input);
            });

            // Auto-save cart changes
            let saveTimeout;
            document.querySelectorAll('.quantity-input').forEach(input => {
                input.addEventListener('change', function() {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        autoSaveCart();
                    }, 1000);
                });
            });
        }

        function validateQuantity(input) {
            const value = parseInt(input.value) || 0;
            const max = parseInt(input.getAttribute('max')) || 999;
            const min = parseInt(input.getAttribute('min')) || 0;

            if (value < min) {
                input.value = min;
                showNotification('warning', 'Minimum quantity is ' + min);
            } else if (value > max) {
                input.value = max;
                showNotification('error', 'Only ' + max + ' items available in stock');
            }

            // Visual feedback for stock levels
            const stockLevel = max;
            if (value > stockLevel * 0.8) {
                input.style.borderColor = 'var(--warning-color)';
                input.style.backgroundColor = '#fff3cd';
            } else {
                input.style.borderColor = 'var(--border-color)';
                input.style.backgroundColor = 'var(--white)';
            }
        }

        function updateItemSubtotal(input) {
            const productId = input.getAttribute('data-product-id');
            const price = parseFloat(input.getAttribute('data-price')) || 0;
            const quantity = parseInt(input.value) || 0;
            const subtotal = price * quantity;

            const subtotalElement = document.getElementById('subtotal-' + productId);
            if (subtotalElement) {
                // Animate the change
                subtotalElement.style.transform = 'scale(1.1)';
                subtotalElement.style.color = 'var(--accent-color)';

                setTimeout(() => {
                    subtotalElement.textContent = '₱' + subtotal.toFixed(2);
                    subtotalElement.style.transform = 'scale(1)';
                    subtotalElement.style.color = 'var(--primary-color)';
                }, 150);
            }
        }

        function updateCartTotals() {
            // This would typically make an AJAX call to update totals
            // For now, we'll show a visual indicator that totals are updating
            const totalElements = document.querySelectorAll('.total-final, .earnings-total');
            totalElements.forEach(element => {
                element.style.opacity = '0.7';
                setTimeout(() => {
                    element.style.opacity = '1';
                }, 300);
            });
        }

        function addQuantityControls(input) {
            const container = input.parentNode;
            container.style.position = 'relative';
            container.style.display = 'flex';
            container.style.alignItems = 'center';
            container.style.justifyContent = 'center';
            container.style.flexDirection = 'column';

            // Create +/- buttons
            const decreaseBtn = document.createElement('button');
            decreaseBtn.innerHTML = '−';
            decreaseBtn.type = 'button';
            decreaseBtn.className = 'quantity-btn';
            decreaseBtn.style.cssText = `
                position: absolute; top: -5px; right: 5px; width: 20px; height: 15px;
                border: none; background: var(--danger-color); color: white;
                border-radius: 3px; font-size: 10px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
            `;

            const increaseBtn = document.createElement('button');
            increaseBtn.innerHTML = '+';
            increaseBtn.type = 'button';
            increaseBtn.className = 'quantity-btn';
            increaseBtn.style.cssText = `
                position: absolute; bottom: -5px; right: 5px; width: 20px; height: 15px;
                border: none; background: var(--accent-color); color: white;
                border-radius: 3px; font-size: 10px; cursor: pointer;
                display: flex; align-items: center; justify-content: center;
            `;

            decreaseBtn.addEventListener('click', () => {
                const currentValue = parseInt(input.value) || 0;
                if (currentValue > 0) {
                    input.value = currentValue - 1;
                    input.dispatchEvent(new Event('change'));
                }
            });

            increaseBtn.addEventListener('click', () => {
                const currentValue = parseInt(input.value) || 0;
                const max = parseInt(input.getAttribute('max')) || 999;
                if (currentValue < max) {
                    input.value = currentValue + 1;
                    input.dispatchEvent(new Event('change'));
                }
            });

            container.appendChild(decreaseBtn);
            container.appendChild(increaseBtn);
        }

        function setupImageErrorHandling() {
            document.querySelectorAll('.product-image, .item-image').forEach(img => {
                img.addEventListener('error', function() {
                    handleImageError(this);
                });

                img.addEventListener('load', function() {
                    this.style.opacity = '1';
                    this.style.transform = 'scale(1)';
                });
            });
        }

        function handleImageError(img) {
            img.style.backgroundColor = 'var(--light-bg)';
            img.style.border = '2px dashed var(--border-color)';
            img.style.display = 'flex';
            img.style.alignItems = 'center';
            img.style.justifyContent = 'center';
            img.style.fontSize = '24px';
            img.style.color = 'var(--text-muted)';
            img.innerHTML = '📦';
            img.title = 'Image not available';

            // Try fallback image
            if (img.src !== '/images/default-product.jpg') {
                setTimeout(() => {
                    img.src = '/images/default-product.jpg';
                }, 1000);
            }
        }

        function setupFormEnhancements() {
            // Enhanced form submission with loading states
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = '⏳ Processing...';
                        submitBtn.disabled = true;

                        // Re-enable after 3 seconds as fallback
                        setTimeout(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }, 3000);
                    }
                });
            });

            // Coupon code enhancement
            const couponInput = document.querySelector('input[name="coupon_code"]');
            if (couponInput) {
                couponInput.addEventListener('input', function() {
                    this.value = this.value.toUpperCase();

                    // Real-time validation
                    const validCoupons = ['WELCOME10', 'SAVE50', 'MEMBER15'];
                    const isValid = validCoupons.includes(this.value);

                    if (this.value.length > 0) {
                        this.style.borderColor = isValid ? 'var(--accent-color)' : 'var(--danger-color)';
                        this.style.backgroundColor = isValid ? '#e8f5e8' : '#f8d7da';
                    } else {
                        this.style.borderColor = 'var(--border-color)';
                        this.style.backgroundColor = 'var(--white)';
                    }
                });
            }
        }

        function setupAnimations() {
            // Animate cart items on hover
            document.querySelectorAll('.cart-item').forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                    this.style.boxShadow = '0 4px 15px rgba(45, 90, 39, 0.1)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // Animate buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Animate progress bars
            document.querySelectorAll('.progress-fill').forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        }

        function autoSaveCart() {
            // This would make an AJAX call to save cart state
            showNotification('info', '💾 Cart auto-saved');
        }

        function showNotification(type, message) {
            const notification = document.createElement('div');
            notification.className = 'notification notification-' + type;
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                padding: 1rem 1.5rem; border-radius: 8px; font-weight: 600;
                min-width: 300px; max-width: 400px;
                animation: slideInRight 0.3s ease;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            `;

            const colors = {
                success: { bg: '#d4edda', color: '#155724', border: '#c3e6cb' },
                error: { bg: '#f8d7da', color: '#721c24', border: '#f5c6cb' },
                warning: { bg: '#fff3cd', color: '#856404', border: '#ffeaa7' },
                info: { bg: '#d1ecf1', color: '#0c5460', border: '#bee5eb' }
            };

            const style = colors[type] || colors.info;
            notification.style.background = style.bg;
            notification.style.color = style.color;
            notification.style.border = '1px solid ' + style.border;

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            notification.innerHTML = (icons[type] || '📢') + ' ' + message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Enhanced confirmation dialogs
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter to checkout
            if (e.ctrlKey && e.key === 'Enter') {
                const checkoutBtn = document.querySelector('button[name="action"][value="checkout"]');
                if (checkoutBtn) {
                    checkoutBtn.click();
                }
            }

            // Escape to clear search/focus
            if (e.key === 'Escape') {
                document.activeElement.blur();
            }
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .cart-item {
                transition: all 0.2s ease;
            }
            .btn {
                transition: all 0.2s ease;
            }
            .progress-fill {
                transition: width 1s ease;
            }
            .quantity-btn:hover {
                transform: scale(1.1);
            }
        `;
        document.head.appendChild(style);

        // Performance monitoring
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`🚀 ExtremeLife Advanced Cart loaded in ${loadTime.toFixed(2)}ms`);

            // Check for layout shifts
            let layoutShiftScore = 0;
            if ('PerformanceObserver' in window) {
                new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (!entry.hadRecentInput) {
                            layoutShiftScore += entry.value;
                        }
                    }
                    console.log(`📊 Cumulative Layout Shift Score: ${layoutShiftScore.toFixed(4)}`);
                }).observe({type: 'layout-shift', buffered: true});
            }
        });

        // Service Worker for offline functionality (if available)
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js').catch(err => {
                console.log('Service Worker registration failed');
            });
        }
    </script>
</body>
</html>
