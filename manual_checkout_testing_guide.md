# 🛒 Manual Checkout Testing Guide - ExtremeLife MLM

## **COMPREHENSIVE CHECKOUT & REBATE VERIFICATION**

**Purpose:** Verify checkout process and 4.00% self-purchase rebate system  
**Target:** Zero failures in checkout flow and accurate rebate calculations  
**Date:** June 18, 2025

---

## 📋 **PRE-TESTING CHECKLIST**

### **✅ System Requirements:**
- [ ] Cart functionality working (test with emergency cart fix)
- [ ] Member authentication system operational
- [ ] Database connectivity established
- [ ] All checkout files accessible

### **✅ Test Environment:**
- **Cart URL:** https://extremelifeherbal.com/enhanced_cart.php
- **Checkout URL:** https://extremelifeherbal.com/mlm/checkout_confirmation.php
- **Test Member ID:** Use existing member or create test account
- **Test Products:** Use cart test functionality

---

## 🧪 **TESTING PROTOCOL**

### **PHASE 1: CART TO CHECKOUT FLOW**

#### **Step 1.1: Cart Functionality Test**
1. **Navigate to:** https://extremelifeherbal.com/enhanced_cart.php
2. **Verify:** Page loads with ExtremeLife branding
3. **Action:** Click "Add Test Product (₱100.00)" button
4. **Expected:** Test product appears in cart
5. **Verify:** Total shows ₱100.00
6. **Check:** Debug info shows session data

**✅ PASS CRITERIA:**
- Cart displays test product
- Total amount calculated correctly
- Session information visible in debug section

#### **Step 1.2: Proceed to Checkout**
1. **Action:** Click "Proceed to Checkout" button
2. **Expected:** Redirects to checkout_confirmation.php
3. **Verify:** Checkout page loads without errors
4. **Check:** Cart items appear on checkout page

**✅ PASS CRITERIA:**
- Successful redirect to checkout
- No PHP errors or blank pages
- Cart items visible on checkout page

---

### **PHASE 2: CHECKOUT PROCESS VERIFICATION**

#### **Step 2.1: Payment Method Selection**
1. **Verify:** Payment options are visible
   - [ ] Cash/Store Pickup option
   - [ ] GCash option (09773382780 - Evelyn Percil)
2. **Action:** Select each payment method
3. **Expected:** Selection works without errors

**✅ PASS CRITERIA:**
- All payment methods selectable
- No JavaScript errors
- Form elements respond correctly

#### **Step 2.2: Order Information Display**
1. **Verify:** Order summary shows:
   - [ ] Product names and quantities
   - [ ] Individual prices
   - [ ] Subtotals
   - [ ] Total amount
   - [ ] Philippine Peso currency (₱)

**✅ PASS CRITERIA:**
- All order details accurate
- Currency formatting correct
- Calculations match cart totals

#### **Step 2.3: Member Information Integration**
1. **Verify:** Member details displayed:
   - [ ] Member name/ID
   - [ ] Member tier/group
   - [ ] Rebate eligibility status
2. **Check:** getMemberInfo function working

**✅ PASS CRITERIA:**
- Member information accurate
- No database connection errors
- Member tier correctly identified

---

### **PHASE 3: REBATE CALCULATION VERIFICATION**

#### **Step 3.1: Self-Purchase Rebate Display**
1. **Verify:** 4.00% rebate calculation shown
2. **Test Cases:**
   - ₱100.00 purchase → ₱4.00 rebate
   - ₱250.00 purchase → ₱10.00 rebate
   - ₱500.00 purchase → ₱20.00 rebate

**✅ PASS CRITERIA:**
- Rebate percentage exactly 4.00%
- Calculations accurate to 2 decimal places
- Rebate amount clearly displayed

#### **Step 3.2: Rebate Integration Test**
1. **Action:** Complete a test purchase
2. **Verify:** Rebate calculation appears in:
   - [ ] Order confirmation
   - [ ] Member dashboard
   - [ ] Transaction history
3. **Check:** Database records updated

**✅ PASS CRITERIA:**
- Rebate amount calculated correctly
- Rebate credited to member account
- Transaction properly logged

---

### **PHASE 4: ORDER COMPLETION TESTING**

#### **Step 4.1: Complete Order Process**
1. **Action:** Fill required fields
2. **Action:** Select payment method
3. **Action:** Click "Complete Order" button
4. **Expected:** Order processing without infinite loops

**🚨 CRITICAL TEST:**
- **NO infinite redirects**
- **NO blank pages**
- **NO PHP fatal errors**
- **NO stuck loading states**

#### **Step 4.2: Order Confirmation**
1. **Expected:** Order confirmation page/message
2. **Verify:** Confirmation includes:
   - [ ] Order number/ID
   - [ ] Order details
   - [ ] Payment method
   - [ ] Rebate amount
   - [ ] Next steps instructions

**✅ PASS CRITERIA:**
- Clear confirmation message
- All order details accurate
- Professional presentation

---

### **PHASE 5: DATABASE VERIFICATION**

#### **Step 5.1: Order Record Verification**
1. **Check:** mlm_orders table
   - [ ] Order record created
   - [ ] Correct member_id
   - [ ] Accurate total_amount
   - [ ] Proper status

#### **Step 5.2: Rebate Record Verification**
1. **Check:** mlm_rebates table
   - [ ] Rebate record created
   - [ ] 4.00% rate recorded
   - [ ] Correct rebate_amount
   - [ ] Linked to order_id

#### **Step 5.3: Transaction History**
1. **Check:** mlm_transactions table
   - [ ] Purchase transaction logged
   - [ ] Rebate transaction logged
   - [ ] Amounts match order

**✅ PASS CRITERIA:**
- All database records accurate
- Proper foreign key relationships
- No orphaned records

---

## 📊 **TESTING SCENARIOS**

### **Scenario A: Small Purchase (₱50-100)**
- **Purpose:** Test minimum rebate calculations
- **Expected Rebate:** ₱2.00-4.00
- **Focus:** Decimal precision

### **Scenario B: Medium Purchase (₱200-500)**
- **Purpose:** Test typical purchase amounts
- **Expected Rebate:** ₱8.00-20.00
- **Focus:** User experience flow

### **Scenario C: Large Purchase (₱1000+)**
- **Purpose:** Test high-value transactions
- **Expected Rebate:** ₱40.00+
- **Focus:** System performance

### **Scenario D: Multiple Items**
- **Purpose:** Test cart with multiple products
- **Expected:** Rebate on total amount
- **Focus:** Calculation accuracy

---

## 🚨 **CRITICAL FAILURE INDICATORS**

### **IMMEDIATE STOP CONDITIONS:**
- ❌ **Infinite redirect loops**
- ❌ **PHP fatal errors**
- ❌ **Blank checkout pages**
- ❌ **Database connection failures**
- ❌ **Incorrect rebate calculations**

### **WARNING CONDITIONS:**
- ⚠️ **Slow page loading (>5 seconds)**
- ⚠️ **Missing payment options**
- ⚠️ **Formatting issues**
- ⚠️ **Session timeout errors**

---

## 📋 **TEST RESULTS DOCUMENTATION**

### **Checkout Flow Results:**
- [ ] Cart to checkout transition: ✅ PASS / ❌ FAIL
- [ ] Payment method selection: ✅ PASS / ❌ FAIL
- [ ] Order completion: ✅ PASS / ❌ FAIL
- [ ] Confirmation display: ✅ PASS / ❌ FAIL

### **Rebate System Results:**
- [ ] 4.00% calculation accuracy: ✅ PASS / ❌ FAIL
- [ ] Rebate display: ✅ PASS / ❌ FAIL
- [ ] Database recording: ✅ PASS / ❌ FAIL
- [ ] Member account credit: ✅ PASS / ❌ FAIL

### **Overall Assessment:**
- **Checkout Process:** ✅ OPERATIONAL / ⚠️ NEEDS ATTENTION / ❌ CRITICAL ISSUES
- **Rebate System:** ✅ OPERATIONAL / ⚠️ NEEDS ATTENTION / ❌ CRITICAL ISSUES
- **Production Ready:** ✅ YES / ❌ NO

---

## 🔧 **TROUBLESHOOTING GUIDE**

### **If Checkout Fails:**
1. Check Apache error logs: `sudo tail -f /var/log/apache2/error.log`
2. Verify file permissions: `ls -la /var/www/html/mlm/checkout_confirmation.php`
3. Test PHP syntax: `php -l checkout_confirmation.php`
4. Check database connectivity

### **If Rebate Calculation Wrong:**
1. Verify 4.00% rate in code
2. Check calculation formula: `(amount * 4.00) / 100`
3. Test with simple amounts (₱100 = ₱4 rebate)
4. Review database field types (DECIMAL precision)

### **If Order Doesn't Complete:**
1. Check for infinite redirects
2. Verify form action URLs
3. Test session management
4. Review POST data handling

---

## 🎯 **SUCCESS CRITERIA**

### **MINIMUM REQUIREMENTS:**
- ✅ **100% checkout completion rate**
- ✅ **Accurate 4.00% rebate calculations**
- ✅ **No infinite loops or errors**
- ✅ **Proper order confirmation**
- ✅ **Database records created correctly**

### **OPTIMAL PERFORMANCE:**
- ✅ **Page load times <3 seconds**
- ✅ **Mobile-responsive design**
- ✅ **Clear user feedback**
- ✅ **Professional presentation**
- ✅ **Comprehensive error handling**

---

**🚀 Execute this testing protocol to ensure the ExtremeLife MLM checkout and rebate systems are production-ready!**
