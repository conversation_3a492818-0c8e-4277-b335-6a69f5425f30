<?php
/**
 * ExtremeLife MLM Production System Comprehensive Analysis
 * Analyzes deployment status and identifies issues
 */

echo "🔍 ExtremeLife MLM Production System Comprehensive Analysis\n";
echo "===========================================================\n\n";

$base_url = "https://extremelifeherbal.com";
$mlm_url = $base_url . "/mlm";
$total_tests = 0;
$passed_tests = 0;
$critical_issues = [];
$warnings = [];
$deployment_status = [];

function testUrl($url, $test_name, $expected_status = 200, $check_content = null) {
    global $total_tests, $passed_tests, $critical_issues, $warnings;
    $total_tests++;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'ExtremeLife MLM Analysis Tool');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ $test_name - CURL Error: $error\n";
        $critical_issues[] = "$test_name - CURL Error: $error";
        return false;
    }
    
    $status_ok = ($http_code === $expected_status);
    $content_ok = true;
    
    if ($check_content && $status_ok) {
        $content_ok = (strpos($response, $check_content) !== false);
    }
    
    if ($status_ok && $content_ok) {
        echo "✅ $test_name - HTTP $http_code";
        if ($check_content) echo " (Content: ✓)";
        echo "\n";
        $passed_tests++;
        return true;
    } else {
        $status = $status_ok ? "✓" : "✗";
        $content = $content_ok ? "✓" : "✗";
        echo "❌ $test_name - HTTP $http_code (Status: $status";
        if ($check_content) echo ", Content: $content";
        echo ")\n";
        
        if ($expected_status === 200 && $http_code === 404) {
            $critical_issues[] = "$test_name - File missing (404)";
        } elseif ($expected_status === 403 && $http_code === 200) {
            $warnings[] = "$test_name - Security issue (should be 403)";
        } else {
            $critical_issues[] = "$test_name - HTTP $http_code (expected $expected_status)";
        }
        return false;
    }
}

echo "📁 DEPLOYMENT LOCATION ANALYSIS\n";
echo "===============================\n";

// Check if files are in /mlm/ subdirectory vs root directory
$file_locations = [
    'member_dashboard.php' => ['mlm' => false, 'root' => false],
    'register.php' => ['mlm' => false, 'root' => false],
    'enhanced_cart.php' => ['mlm' => false, 'root' => false],
    'login.php' => ['mlm' => false, 'root' => false],
    'genealogy_tree_unilevel.php' => ['mlm' => false, 'root' => false]
];

foreach ($file_locations as $file => $locations) {
    // Check MLM subdirectory
    $mlm_result = testUrl("$mlm_url/$file", "MLM/$file location check");
    $file_locations[$file]['mlm'] = $mlm_result;
    
    // Check root directory
    $root_result = testUrl("$base_url/$file", "ROOT/$file location check");
    $file_locations[$file]['root'] = $root_result;
}

echo "\n🔍 FILE DEPLOYMENT STATUS SUMMARY\n";
echo "=================================\n";

foreach ($file_locations as $file => $locations) {
    echo "📄 $file:\n";
    echo "   MLM directory (/mlm/): " . ($locations['mlm'] ? "✅ Found" : "❌ Missing") . "\n";
    echo "   Root directory (/): " . ($locations['root'] ? "✅ Found" : "❌ Missing") . "\n";
    
    if ($locations['mlm'] && $locations['root']) {
        $warnings[] = "$file exists in both locations - potential conflict";
        echo "   ⚠️ DUPLICATE: File exists in both locations\n";
    } elseif (!$locations['mlm'] && !$locations['root']) {
        $critical_issues[] = "$file missing from both locations";
        echo "   🔴 CRITICAL: File missing from both locations\n";
    } elseif ($locations['root'] && !$locations['mlm']) {
        $warnings[] = "$file in root instead of MLM directory";
        echo "   🟡 WARNING: File in root directory instead of /mlm/\n";
    }
    echo "\n";
}

echo "🔐 SECURITY AND CONFIGURATION ANALYSIS\n";
echo "======================================\n";

// Test config file security
testUrl("$mlm_url/config/database.php", "MLM Config Security", 403);
testUrl("$base_url/config/database.php", "Root Config Security", 403);
testUrl("$mlm_url/includes/auth.php", "MLM Includes Security", 403);
testUrl("$base_url/includes/auth.php", "Root Includes Security", 403);

echo "\n🎨 BRANDING AND FUNCTIONALITY ANALYSIS\n";
echo "======================================\n";

// Test branding consistency
testUrl("$base_url/login.php", "ExtremeLife Branding", 200, "ExtremeLife");
testUrl("$base_url/register.php", "Philippine Peso Currency", 200, "₱");
testUrl("$base_url/enhanced_cart.php", "Cart Functionality", 200, "Cart");

echo "\n🔧 AUTHENTICATION SYSTEM ANALYSIS\n";
echo "=================================\n";

// Test authentication redirects
testUrl("$mlm_url/genealogy_tree_unilevel.php", "MLM Auth Redirect", 200, "login");
testUrl("$base_url/genealogy_tree.php", "Root Auth System", 200);

echo "\n📊 ADMIN INTERFACE ANALYSIS\n";
echo "===========================\n";

// Test admin interfaces
testUrl("$mlm_url/admin/admin_commission_management.php", "MLM Admin Interface");
testUrl("$base_url/admin/login.php", "Root Admin Interface");

echo "\n" . str_repeat("=", 70) . "\n";
echo "🎯 COMPREHENSIVE ANALYSIS SUMMARY\n";
echo str_repeat("=", 70) . "\n";

echo "Total Tests: $total_tests\n";
echo "Passed: $passed_tests\n";
echo "Failed: " . ($total_tests - $passed_tests) . "\n";
echo "Success Rate: " . round(($passed_tests / $total_tests) * 100, 1) . "%\n\n";

if (count($critical_issues) > 0) {
    echo "🚨 CRITICAL ISSUES (" . count($critical_issues) . "):\n";
    echo str_repeat("=", 30) . "\n";
    foreach ($critical_issues as $issue) {
        echo "🔴 $issue\n";
    }
    echo "\n";
}

if (count($warnings) > 0) {
    echo "⚠️ WARNINGS (" . count($warnings) . "):\n";
    echo str_repeat("=", 20) . "\n";
    foreach ($warnings as $warning) {
        echo "🟡 $warning\n";
    }
    echo "\n";
}

echo "📋 DEPLOYMENT ANALYSIS CONCLUSIONS\n";
echo "==================================\n";

$files_in_root = 0;
$files_in_mlm = 0;
$missing_files = 0;
$duplicate_files = 0;

foreach ($file_locations as $file => $locations) {
    if ($locations['root']) $files_in_root++;
    if ($locations['mlm']) $files_in_mlm++;
    if (!$locations['root'] && !$locations['mlm']) $missing_files++;
    if ($locations['root'] && $locations['mlm']) $duplicate_files++;
}

echo "📊 File Distribution:\n";
echo "   Files in ROOT directory: $files_in_root\n";
echo "   Files in MLM directory: $files_in_mlm\n";
echo "   Missing files: $missing_files\n";
echo "   Duplicate files: $duplicate_files\n\n";

if ($files_in_root > $files_in_mlm) {
    echo "🔍 FINDING: Most files are deployed in ROOT directory instead of /mlm/\n";
    echo "📋 RECOMMENDATION: Files should be in /mlm/ subdirectory for proper organization\n\n";
}

if ($missing_files > 0) {
    echo "🚨 CRITICAL: $missing_files files are completely missing from production\n";
    echo "📋 ACTION REQUIRED: Deploy missing files immediately\n\n";
}

if ($duplicate_files > 0) {
    echo "⚠️ WARNING: $duplicate_files files exist in multiple locations\n";
    echo "📋 RECOMMENDATION: Remove duplicates to prevent conflicts\n\n";
}

echo "🎯 OVERALL SYSTEM STATUS:\n";
if ($passed_tests >= ($total_tests * 0.8)) {
    echo "✅ GOOD: System is mostly functional\n";
} elseif ($passed_tests >= ($total_tests * 0.6)) {
    echo "🟡 FAIR: System has significant issues but core functionality works\n";
} else {
    echo "🔴 POOR: System has critical issues requiring immediate attention\n";
}

echo "\n🚀 NEXT STEPS:\n";
echo "1. Address critical issues first\n";
echo "2. Resolve file location conflicts\n";
echo "3. Deploy missing files\n";
echo "4. Secure configuration files\n";
echo "5. Test full system functionality\n";
?>
