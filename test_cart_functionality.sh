#!/bin/bash

# Test Cart Functionality - ExtremeLife MLM Production
# Verifies that cart functionality is working correctly

echo "🧪 Testing Cart Functionality - ExtremeLife MLM"
echo "==============================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PASSED=0
FAILED=0
TOTAL=0

test_result() {
    TOTAL=$((TOTAL + 1))
    if [ $1 -eq 0 ]; then
        echo -e "✅ $2"
        PASSED=$((PASSED + 1))
    else
        echo -e "❌ $2"
        FAILED=$((FAILED + 1))
    fi
}

echo -e "${YELLOW}Testing cart functionality after fixes...${NC}"
echo ""

# 1. Test cart page accessibility
echo -e "${BLUE}TEST 1: Cart Page Accessibility${NC}"
CART_RESPONSE=$(curl -s -I https://extremelifeherbal.com/enhanced_cart.php 2>/dev/null)
if echo "$CART_RESPONSE" | grep -q "200 OK\|302 Found"; then
    test_result 0 "Cart page is accessible"
else
    test_result 1 "Cart page is not accessible"
fi

# 2. Test cart content
echo -e "${BLUE}TEST 2: Cart Content Loading${NC}"
CART_CONTENT=$(curl -s https://extremelifeherbal.com/enhanced_cart.php 2>/dev/null)
if echo "$CART_CONTENT" | grep -q "ExtremeLife.*Cart"; then
    test_result 0 "Cart page loads with ExtremeLife branding"
else
    test_result 1 "Cart page does not load properly"
fi

# 3. Test session handling
echo -e "${BLUE}TEST 3: Session Handling${NC}"
if echo "$CART_CONTENT" | grep -q "Session ID\|session_id"; then
    test_result 0 "Session information is displayed"
else
    test_result 1 "Session information not found"
fi

# 4. Test database connectivity
echo -e "${BLUE}TEST 4: Database Connectivity${NC}"
if echo "$CART_CONTENT" | grep -q "Database connection failed"; then
    test_result 1 "Database connection error detected"
else
    test_result 0 "No database connection errors"
fi

# 5. Test cart debug information
echo -e "${BLUE}TEST 5: Debug Information${NC}"
if echo "$CART_CONTENT" | grep -q "Debug Info"; then
    test_result 0 "Debug information is available"
else
    test_result 1 "Debug information not found"
fi

# 6. Test PHP errors
echo -e "${BLUE}TEST 6: PHP Error Check${NC}"
if echo "$CART_CONTENT" | grep -q "Fatal error\|Parse error\|Warning"; then
    test_result 1 "PHP errors detected in cart"
else
    test_result 0 "No PHP errors in cart"
fi

# 7. Test cart form elements
echo -e "${BLUE}TEST 7: Cart Form Elements${NC}"
if echo "$CART_CONTENT" | grep -q "Test Add Item\|Add Test Product"; then
    test_result 0 "Test add item form is present"
else
    test_result 1 "Test add item form not found"
fi

# 8. Test currency formatting
echo -e "${BLUE}TEST 8: Currency Formatting${NC}"
if echo "$CART_CONTENT" | grep -q "₱"; then
    test_result 0 "Philippine Peso currency formatting present"
else
    test_result 1 "Philippine Peso currency formatting missing"
fi

# 9. Test navigation links
echo -e "${BLUE}TEST 9: Navigation Links${NC}"
if echo "$CART_CONTENT" | grep -q "Dashboard\|Login\|Home"; then
    test_result 0 "Navigation links are present"
else
    test_result 1 "Navigation links missing"
fi

# 10. Test session directory permissions
echo -e "${BLUE}TEST 10: Session Directory${NC}"
SESSION_PATH=$(php -r 'echo session_save_path();')
if [ -w "$SESSION_PATH" ]; then
    test_result 0 "Session directory is writable"
else
    test_result 1 "Session directory is not writable"
fi

echo ""
echo "========================================"
echo -e "${BLUE}🎯 CART FUNCTIONALITY TEST SUMMARY${NC}"
echo "========================================"
echo "Total Tests: $TOTAL"
echo "Passed: $PASSED"
echo "Failed: $FAILED"
echo "Success Rate: $(( PASSED * 100 / TOTAL ))%"
echo ""

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL CART TESTS PASSED!${NC}"
    echo -e "${GREEN}✅ Cart functionality is working correctly${NC}"
    echo -e "${GREEN}✅ Session handling is operational${NC}"
    echo -e "${GREEN}✅ Database connectivity established${NC}"
    echo -e "${GREEN}✅ Debug information available${NC}"
    echo ""
    echo -e "${YELLOW}📋 MANUAL TESTING STEPS:${NC}"
    echo -e "${YELLOW}1. Visit: https://extremelifeherbal.com/enhanced_cart.php${NC}"
    echo -e "${YELLOW}2. Click 'Add Test Product' button${NC}"
    echo -e "${YELLOW}3. Verify item appears in cart${NC}"
    echo -e "${YELLOW}4. Test quantity updates${NC}"
    echo -e "${YELLOW}5. Test remove item functionality${NC}"
    echo ""
    echo -e "${GREEN}🚀 Cart is ready for production use!${NC}"
    
elif [ $FAILED -le 3 ]; then
    echo -e "${YELLOW}⚠️ Most cart functionality working ($FAILED issues)${NC}"
    echo -e "${YELLOW}📋 Minor issues detected - review failed tests${NC}"
    echo ""
    echo -e "${BLUE}🔧 TROUBLESHOOTING:${NC}"
    echo "1. Check specific failed tests above"
    echo "2. Review Apache error logs"
    echo "3. Test manual cart operations"
    echo "4. Verify session permissions"
    
else
    echo -e "${RED}🚨 Cart functionality needs attention ($FAILED failures)${NC}"
    echo -e "${RED}📋 Multiple cart issues detected${NC}"
    echo ""
    echo -e "${BLUE}🔧 TROUBLESHOOTING:${NC}"
    echo "1. Re-run: bash fix_cart_functionality.sh"
    echo "2. Check Apache error logs: sudo tail -f /var/log/apache2/error.log"
    echo "3. Verify database connectivity"
    echo "4. Check session configuration"
fi

echo ""
echo -e "${BLUE}📋 CART URLS FOR TESTING:${NC}"
echo "• Direct: https://extremelifeherbal.com/mlm/enhanced_cart.php"
echo "• Redirect: https://extremelifeherbal.com/enhanced_cart.php"
echo ""

# Show current cart content sample
echo -e "${BLUE}📄 CART CONTENT SAMPLE:${NC}"
echo "First 10 lines of cart response:"
echo "$CART_CONTENT" | head -10

echo ""
echo -e "${YELLOW}💡 TIP: Use the debug information on the cart page to troubleshoot session issues${NC}"
