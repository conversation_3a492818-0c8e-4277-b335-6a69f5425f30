#!/bin/bash

# ExtremeLife MLM Production Server Critical Fixes
# Server: *************** (extremelifeherbal.com)
# Execute this script on the production server to fix deployment issues

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${RED}🚨 EXTREMELIFE MLM PRODUCTION CRITICAL FIXES${NC}"
echo -e "${RED}=============================================${NC}"
echo ""
echo -e "${YELLOW}Server: *************** (extremelifeherbal.com)${NC}"
echo -e "${YELLOW}Target: Achieve 95%+ system functionality${NC}"
echo -e "${YELLOW}Current: 57.1% functionality (mixed deployment)${NC}"
echo -e "${YELLOW}Timestamp: $(date)${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    SUDO=""
else
    SUDO="sudo"
fi

print_info "Starting ExtremeLife MLM Production Fixes..."

# PHASE 1: CREATE BACKUP
print_info "PHASE 1: Creating backup of current state..."
BACKUP_DIR="/var/www/html_backup_$(date +%Y%m%d_%H%M%S)"
$SUDO cp -r /var/www/html $BACKUP_DIR
print_status "Backup created at: $BACKUP_DIR"

# PHASE 2: FILE REORGANIZATION
print_info "PHASE 2: Reorganizing files to correct locations..."

# Check if files exist before moving
if [ -f "/var/www/html/member_dashboard.php" ]; then
    print_info "Moving member_dashboard.php to /mlm/ directory..."
    $SUDO mv /var/www/html/member_dashboard.php /var/www/html/mlm/
    print_status "member_dashboard.php moved successfully"
else
    print_warning "member_dashboard.php not found in root directory"
fi

if [ -f "/var/www/html/register.php" ]; then
    print_info "Moving register.php to /mlm/ directory..."
    $SUDO mv /var/www/html/register.php /var/www/html/mlm/
    print_status "register.php moved successfully"
else
    print_warning "register.php not found in root directory"
fi

if [ -f "/var/www/html/enhanced_cart.php" ]; then
    print_info "Moving enhanced_cart.php to /mlm/ directory..."
    $SUDO mv /var/www/html/enhanced_cart.php /var/www/html/mlm/
    print_status "enhanced_cart.php moved successfully"
else
    print_warning "enhanced_cart.php not found in root directory"
fi

# Remove duplicate login.php from root (keep MLM version)
if [ -f "/var/www/html/login.php" ]; then
    print_info "Removing duplicate login.php from root directory..."
    $SUDO rm /var/www/html/login.php
    print_status "Duplicate login.php removed"
else
    print_info "No duplicate login.php found in root directory"
fi

# PHASE 3: SECURITY CONFIGURATION
print_info "PHASE 3: Configuring security for sensitive directories..."

# Create .htaccess for config directory
print_info "Securing config directory..."
$SUDO mkdir -p /var/www/html/mlm/config
$SUDO cat > /tmp/config_htaccess << 'EOF'
Order deny,allow
Deny from all
EOF
$SUDO mv /tmp/config_htaccess /var/www/html/mlm/config/.htaccess
print_status "Config directory secured with .htaccess"

# Create .htaccess for includes directory
print_info "Securing includes directory..."
$SUDO mkdir -p /var/www/html/mlm/includes
$SUDO cat > /tmp/includes_htaccess << 'EOF'
Order deny,allow
Deny from all
EOF
$SUDO mv /tmp/includes_htaccess /var/www/html/mlm/includes/.htaccess
print_status "Includes directory secured with .htaccess"

# PHASE 4: FILE PERMISSIONS AND OWNERSHIP
print_info "PHASE 4: Setting proper file permissions and ownership..."

# Set ownership to www-data
print_info "Setting ownership to www-data:www-data..."
$SUDO chown -R www-data:www-data /var/www/html/mlm/
print_status "Ownership set to www-data:www-data"

# Set file permissions
print_info "Setting file permissions..."
$SUDO find /var/www/html/mlm/ -type f -name "*.php" -exec chmod 644 {} \;
$SUDO find /var/www/html/mlm/ -type d -exec chmod 755 {} \;
$SUDO chmod 644 /var/www/html/mlm/config/.htaccess
$SUDO chmod 644 /var/www/html/mlm/includes/.htaccess
print_status "File permissions set (644 for PHP files, 755 for directories)"

# PHASE 5: CREATE MLM INDEX FILE
print_info "PHASE 5: Creating MLM directory index file..."
$SUDO cat > /tmp/mlm_index << 'EOF'
<?php
/**
 * ExtremeLife MLM System - Main Directory Index
 * Redirects to login page for security
 */

session_start();

// Check if user is already logged in
if (isset($_SESSION['member_id'])) {
    header('Location: member_dashboard.php');
    exit;
}

// Redirect non-logged-in users to login
header('Location: login.php');
exit;
?>
EOF
$SUDO mv /tmp/mlm_index /var/www/html/mlm/index.php
$SUDO chown www-data:www-data /var/www/html/mlm/index.php
$SUDO chmod 644 /var/www/html/mlm/index.php
print_status "MLM index.php created"

# PHASE 6: RESTART APACHE
print_info "PHASE 6: Restarting Apache web server..."
$SUDO systemctl restart apache2
if $SUDO systemctl is-active --quiet apache2; then
    print_status "Apache restarted successfully"
else
    print_error "Apache restart failed - check configuration"
    exit 1
fi

# PHASE 7: VERIFICATION
print_info "PHASE 7: Running basic verification tests..."

# Test if Apache is running
if $SUDO systemctl is-active --quiet apache2; then
    print_status "Apache is running"
else
    print_error "Apache is not running"
fi

# Check if files exist in correct locations
FILES_TO_CHECK=(
    "/var/www/html/mlm/member_dashboard.php"
    "/var/www/html/mlm/register.php"
    "/var/www/html/mlm/enhanced_cart.php"
    "/var/www/html/mlm/login.php"
    "/var/www/html/mlm/genealogy_tree_unilevel.php"
    "/var/www/html/mlm/index.php"
)

for file in "${FILES_TO_CHECK[@]}"; do
    if [ -f "$file" ]; then
        print_status "$(basename $file) exists in MLM directory"
    else
        print_error "$(basename $file) missing from MLM directory"
    fi
done

# Check security files
SECURITY_FILES=(
    "/var/www/html/mlm/config/.htaccess"
    "/var/www/html/mlm/includes/.htaccess"
)

for file in "${SECURITY_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "$(dirname $file | xargs basename)/.htaccess security file exists"
    else
        print_error "$(dirname $file | xargs basename)/.htaccess security file missing"
    fi
done

echo ""
echo -e "${GREEN}🎉 EXTREMELIFE MLM PRODUCTION FIXES COMPLETED!${NC}"
echo -e "${GREEN}===============================================${NC}"
echo ""
echo -e "${YELLOW}📊 SUMMARY OF CHANGES:${NC}"
echo -e "${YELLOW}• Files moved to correct /mlm/ directory${NC}"
echo -e "${YELLOW}• Duplicate files removed${NC}"
echo -e "${YELLOW}• Security configured for config/includes${NC}"
echo -e "${YELLOW}• Proper file permissions set${NC}"
echo -e "${YELLOW}• Apache restarted${NC}"
echo ""
echo -e "${BLUE}📋 NEXT STEPS:${NC}"
echo -e "${BLUE}1. Test URLs: https://extremelifeherbal.com/mlm/${NC}"
echo -e "${BLUE}2. Run post_fix_verification.php script${NC}"
echo -e "${BLUE}3. Test login and registration functionality${NC}"
echo -e "${BLUE}4. Verify admin panel access${NC}"
echo ""
echo -e "${GREEN}🚀 Expected Result: 95%+ system functionality${NC}"
echo -e "${GREEN}🔒 Security: Config and includes directories secured${NC}"
echo -e "${GREEN}📁 Organization: All MLM files in correct locations${NC}"
echo ""
echo -e "${YELLOW}Backup Location: $BACKUP_DIR${NC}"
echo -e "${YELLOW}Rollback: sudo cp -r $BACKUP_DIR/* /var/www/html/${NC}"
