<?php
/**
 * ExtremeLife MLM Authentication Helper
 * Provides authentication functions for the MLM system
 */

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['member_id']) && !empty($_SESSION['member_id']);
}

/**
 * Get current logged in member ID
 */
function getCurrentMemberId() {
    return $_SESSION['member_id'] ?? null;
}

/**
 * Get current logged in member info
 */
function getCurrentMember() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=drupal_umd', 'drupal_user', 'secure_drupal_pass_1748318545');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("SELECT * FROM mlm_members WHERE id = ? AND status = 'active'");
        $stmt->execute([$_SESSION['member_id']]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        error_log("Error getting current member: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if user has admin privileges
 */
function isAdmin() {
    if (!isLoggedIn()) {
        return false;
    }
    
    $member = getCurrentMember();
    return $member && ($member['user_role'] === 'admin' || $member['user_role'] === 'super_admin');
}

/**
 * Require login - redirect to login page if not logged in
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

/**
 * Require admin privileges
 */
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: member_dashboard.php?error=access_denied');
        exit;
    }
}

/**
 * Get member info by ID
 */
function getMemberInfo($member_id) {
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=drupal_umd', 'drupal_user', 'secure_drupal_pass_1748318545');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT m.*, ug.group_name, ug.commission_rate as group_commission_rate
            FROM mlm_members m
            LEFT JOIN mlm_user_groups ug ON m.user_group_id = ug.id
            WHERE m.id = ?
        ");
        $stmt->execute([$member_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return [
                'id' => $member_id,
                'first_name' => 'Member',
                'last_name' => '',
                'user_group_id' => 1,
                'group_name' => 'Member',
                'group_commission_rate' => 10
            ];
        }
        
        return $result;
        
    } catch (PDOException $e) {
        error_log("Error getting member info: " . $e->getMessage());
        return [
            'id' => $member_id,
            'first_name' => 'Member',
            'last_name' => '',
            'user_group_id' => 1,
            'group_name' => 'Member',
            'group_commission_rate' => 10
        ];
    }
}

/**
 * Format currency for Philippine Peso
 */
function formatCurrency($amount) {
    if (is_null($amount) || $amount === '') {
        return '₱0.00';
    }
    return '₱' . number_format((float)$amount, 2);
}

/**
 * Format number safely
 */
function formatNumber($number, $decimals = 0) {
    if (is_null($number) || $number === '') {
        return '0';
    }
    return number_format((float)$number, $decimals);
}

/**
 * Safe HTML output
 */
function safeOutput($text) {
    return htmlspecialchars($text ?? '', ENT_QUOTES, 'UTF-8');
}
?>
