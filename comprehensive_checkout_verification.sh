#!/bin/bash

# Comprehensive Checkout Process Verification - ExtremeLife MLM
# Tests checkout flow and self-purchase rebate calculations

echo "🛒 Comprehensive Checkout Process Verification - ExtremeLife MLM"
echo "=============================================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PASSED=0
FAILED=0
TOTAL=0
CRITICAL_ISSUES=()
WARNINGS=()

test_result() {
    TOTAL=$((TOTAL + 1))
    if [ $1 -eq 0 ]; then
        echo -e "✅ $2"
        PASSED=$((PASSED + 1))
    else
        echo -e "❌ $2"
        FAILED=$((FAILED + 1))
        if [ "$3" = "critical" ]; then
            CRITICAL_ISSUES+=("$2")
        else
            WARNINGS+=("$2")
        fi
    fi
}

echo -e "${YELLOW}Testing checkout process and rebate calculations...${NC}"
echo ""

# SECTION 1: CHECKOUT PROCESS VERIFICATION
echo -e "${BLUE}📋 SECTION 1: CHECKOUT PROCESS VERIFICATION${NC}"
echo "============================================="

# 1.1 Test checkout_confirmation.php accessibility
echo -e "${BLUE}TEST 1.1: Checkout Confirmation Page Accessibility${NC}"
CHECKOUT_RESPONSE=$(curl -s -I https://extremelifeherbal.com/mlm/checkout_confirmation.php 2>/dev/null)
CHECKOUT_HTTP=$(echo "$CHECKOUT_RESPONSE" | grep -i "HTTP" | head -1)

if echo "$CHECKOUT_RESPONSE" | grep -q "200 OK\|302 Found"; then
    test_result 0 "Checkout confirmation page is accessible"
else
    test_result 1 "Checkout confirmation page not accessible" "critical"
    echo "Response: $CHECKOUT_HTTP"
fi

# 1.2 Test checkout content loading
echo -e "${BLUE}TEST 1.2: Checkout Content Loading${NC}"
CHECKOUT_CONTENT=$(curl -s https://extremelifeherbal.com/mlm/checkout_confirmation.php 2>/dev/null)

if echo "$CHECKOUT_CONTENT" | grep -q "ExtremeLife.*Checkout\|Order.*Confirmation"; then
    test_result 0 "Checkout page loads with proper content"
else
    test_result 1 "Checkout page content not loading properly" "critical"
fi

# 1.3 Test payment method options
echo -e "${BLUE}TEST 1.3: Payment Method Options${NC}"
if echo "$CHECKOUT_CONTENT" | grep -q "Cash.*Store.*Pickup\|GCash\|Payment.*Method"; then
    test_result 0 "Payment method options are present"
    
    # Check for specific payment methods
    if echo "$CHECKOUT_CONTENT" | grep -q -i "cash"; then
        echo "  ✓ Cash payment option found"
    fi
    if echo "$CHECKOUT_CONTENT" | grep -q -i "store.*pickup\|pickup"; then
        echo "  ✓ Store pickup option found"
    fi
    if echo "$CHECKOUT_CONTENT" | grep -q -i "gcash"; then
        echo "  ✓ GCash payment option found"
    fi
else
    test_result 1 "Payment method options missing" "critical"
fi

# 1.4 Test for PHP errors in checkout
echo -e "${BLUE}TEST 1.4: PHP Error Check in Checkout${NC}"
if echo "$CHECKOUT_CONTENT" | grep -q "Fatal error\|Parse error\|Warning.*error\|Notice.*error"; then
    test_result 1 "PHP errors detected in checkout page" "critical"
    echo "Errors found:"
    echo "$CHECKOUT_CONTENT" | grep -i "error\|warning\|notice" | head -5
else
    test_result 0 "No PHP errors in checkout page"
fi

# 1.5 Test checkout form elements
echo -e "${BLUE}TEST 1.5: Checkout Form Elements${NC}"
if echo "$CHECKOUT_CONTENT" | grep -q "form.*method.*post\|Complete.*Order\|Proceed.*Checkout"; then
    test_result 0 "Checkout form elements are present"
else
    test_result 1 "Checkout form elements missing" "critical"
fi

echo ""

# SECTION 2: DATABASE STRUCTURE VERIFICATION
echo -e "${BLUE}📋 SECTION 2: DATABASE STRUCTURE VERIFICATION${NC}"
echo "=============================================="

# 2.1 Test database connectivity for checkout
echo -e "${BLUE}TEST 2.1: Database Connectivity${NC}"
cat > /tmp/test_checkout_db.php << 'EOF'
<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=drupal_umd', 'drupal_user', 'secure_drupal_pass_1748318545');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n";
    
    // Test required tables for checkout and rebates
    $required_tables = [
        'mlm_members' => 'Member data',
        'mlm_products' => 'Product catalog',
        'mlm_orders' => 'Order tracking',
        'mlm_order_items' => 'Order details',
        'mlm_rebates' => 'Rebate calculations',
        'mlm_transactions' => 'Transaction history',
        'mlm_user_groups' => 'Member tiers'
    ];
    
    foreach ($required_tables as $table => $description) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ $table table exists ($description)\n";
            
            // Get table structure for critical tables
            if (in_array($table, ['mlm_rebates', 'mlm_transactions', 'mlm_orders'])) {
                $stmt = $pdo->query("DESCRIBE $table");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "   Columns: " . implode(', ', array_slice($columns, 0, 5)) . "\n";
            }
        } else {
            echo "❌ $table table missing ($description)\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
}
?>
EOF

DB_TEST_RESULT=$(php /tmp/test_checkout_db.php 2>&1)
echo "$DB_TEST_RESULT"

if echo "$DB_TEST_RESULT" | grep -q "Database connection successful"; then
    test_result 0 "Database connectivity working"
else
    test_result 1 "Database connectivity issues" "critical"
fi

# Check for required tables
REQUIRED_TABLES=("mlm_orders" "mlm_rebates" "mlm_transactions")
for table in "${REQUIRED_TABLES[@]}"; do
    if echo "$DB_TEST_RESULT" | grep -q "$table table exists"; then
        test_result 0 "$table table exists"
    else
        test_result 1 "$table table missing" "critical"
    fi
done

echo ""

# SECTION 3: REBATE CALCULATION VERIFICATION
echo -e "${BLUE}📋 SECTION 3: REBATE CALCULATION VERIFICATION${NC}"
echo "============================================="

# 3.1 Test rebate calculation logic
echo -e "${BLUE}TEST 3.1: Rebate Calculation Logic${NC}"
cat > /tmp/test_rebate_calculation.php << 'EOF'
<?php
// Test 4.00% self-purchase rebate calculation

function calculateSelfPurchaseRebate($purchase_amount) {
    $rebate_rate = 4.00; // 4.00% rebate rate
    return ($purchase_amount * $rebate_rate) / 100;
}

// Test cases
$test_cases = [
    100.00,   // ₱100 -> ₱4.00 rebate
    250.50,   // ₱250.50 -> ₱10.02 rebate
    1000.00,  // ₱1000 -> ₱40.00 rebate
    75.25     // ₱75.25 -> ₱3.01 rebate
];

echo "🧮 Testing 4.00% Self-Purchase Rebate Calculations:\n";
echo "================================================\n";

foreach ($test_cases as $amount) {
    $rebate = calculateSelfPurchaseRebate($amount);
    $formatted_amount = '₱' . number_format($amount, 2);
    $formatted_rebate = '₱' . number_format($rebate, 2);
    echo "Purchase: $formatted_amount -> Rebate: $formatted_rebate (4.00%)\n";
}

echo "\n✅ Rebate calculation logic test completed\n";

// Test rebate rate validation
$expected_rate = 4.00;
echo "\n🔍 Rebate Rate Verification:\n";
echo "Expected Rate: $expected_rate%\n";
echo "Calculation Method: (amount * rate) / 100\n";

// Verify calculation accuracy
$test_amount = 100.00;
$calculated_rebate = calculateSelfPurchaseRebate($test_amount);
$expected_rebate = 4.00;

if (abs($calculated_rebate - $expected_rebate) < 0.01) {
    echo "✅ Rebate calculation accuracy verified\n";
} else {
    echo "❌ Rebate calculation inaccurate\n";
    echo "Expected: ₱$expected_rebate, Got: ₱$calculated_rebate\n";
}
?>
EOF

REBATE_TEST_RESULT=$(php /tmp/test_rebate_calculation.php 2>&1)
echo "$REBATE_TEST_RESULT"

if echo "$REBATE_TEST_RESULT" | grep -q "Rebate calculation accuracy verified"; then
    test_result 0 "4.00% rebate calculation logic is accurate"
else
    test_result 1 "Rebate calculation logic has issues" "critical"
fi

# 3.2 Test rebate integration in checkout files
echo -e "${BLUE}TEST 3.2: Rebate Integration in Checkout Files${NC}"
CHECKOUT_FILES=("/var/www/html/mlm/checkout_confirmation.php" "/var/www/html/mlm/enhanced_cart.php")

for file in "${CHECKOUT_FILES[@]}"; do
    if [ -f "$file" ]; then
        if grep -q "rebate\|4\.00\|4%" "$file"; then
            test_result 0 "Rebate logic found in $(basename $file)"
        else
            test_result 1 "Rebate logic missing in $(basename $file)" "warning"
        fi
    else
        test_result 1 "$(basename $file) file not found" "critical"
    fi
done

echo ""

# SECTION 4: CHECKOUT FLOW SIMULATION
echo -e "${BLUE}📋 SECTION 4: CHECKOUT FLOW SIMULATION${NC}"
echo "======================================"

# 4.1 Test checkout redirect chain
echo -e "${BLUE}TEST 4.1: Checkout Redirect Chain${NC}"

# Test cart to checkout flow
CART_TO_CHECKOUT=$(curl -s -I https://extremelifeherbal.com/enhanced_cart.php 2>/dev/null)
if echo "$CART_TO_CHECKOUT" | grep -q "301\|302\|200"; then
    test_result 0 "Cart page accessible for checkout flow"
else
    test_result 1 "Cart page not accessible" "critical"
fi

# 4.2 Test checkout form submission capability
echo -e "${BLUE}TEST 4.2: Checkout Form Submission Capability${NC}"
if echo "$CHECKOUT_CONTENT" | grep -q "action.*=.*['\"].*['\"].*method.*=.*post\|method.*=.*post.*action"; then
    test_result 0 "Checkout form has proper action and method"
else
    test_result 1 "Checkout form missing proper action/method" "critical"
fi

# 4.3 Test for infinite loop prevention
echo -e "${BLUE}TEST 4.3: Infinite Loop Prevention${NC}"
if echo "$CHECKOUT_CONTENT" | grep -q "Location:.*checkout.*Location:.*checkout"; then
    test_result 1 "Potential infinite redirect loop detected" "critical"
else
    test_result 0 "No infinite redirect loops detected"
fi

echo ""

# SECTION 5: MEMBER INTEGRATION VERIFICATION
echo -e "${BLUE}📋 SECTION 5: MEMBER INTEGRATION VERIFICATION${NC}"
echo "============================================="

# 5.1 Test member authentication in checkout
echo -e "${BLUE}TEST 5.1: Member Authentication in Checkout${NC}"
if echo "$CHECKOUT_CONTENT" | grep -q "session\|member_id\|\$_SESSION"; then
    test_result 0 "Member session handling present in checkout"
else
    test_result 1 "Member session handling missing" "warning"
fi

# 5.2 Test getMemberInfo function availability
echo -e "${BLUE}TEST 5.2: getMemberInfo Function Availability${NC}"
if echo "$CHECKOUT_CONTENT" | grep -q "getMemberInfo\|function.*getMemberInfo"; then
    test_result 0 "getMemberInfo function found in checkout"
else
    test_result 1 "getMemberInfo function missing" "warning"
fi

echo ""

# SECTION 6: COMPREHENSIVE SUMMARY
echo "========================================"
echo -e "${BLUE}🎯 COMPREHENSIVE VERIFICATION SUMMARY${NC}"
echo "========================================"
echo "Total Tests: $TOTAL"
echo "Passed: $PASSED"
echo "Failed: $FAILED"
echo "Success Rate: $(( PASSED * 100 / TOTAL ))%"
echo ""

if [ ${#CRITICAL_ISSUES[@]} -gt 0 ]; then
    echo -e "${RED}🚨 CRITICAL ISSUES (${#CRITICAL_ISSUES[@]}):${NC}"
    for issue in "${CRITICAL_ISSUES[@]}"; do
        echo -e "${RED}  • $issue${NC}"
    done
    echo ""
fi

if [ ${#WARNINGS[@]} -gt 0 ]; then
    echo -e "${YELLOW}⚠️ WARNINGS (${#WARNINGS[@]}):${NC}"
    for warning in "${WARNINGS[@]}"; do
        echo -e "${YELLOW}  • $warning${NC}"
    done
    echo ""
fi

# Overall assessment
if [ ${#CRITICAL_ISSUES[@]} -eq 0 ] && [ $FAILED -le 2 ]; then
    echo -e "${GREEN}🎉 CHECKOUT SYSTEM STATUS: OPERATIONAL${NC}"
    echo -e "${GREEN}✅ Checkout process appears functional${NC}"
    echo -e "${GREEN}✅ Database structure adequate${NC}"
    echo -e "${GREEN}✅ Rebate calculations accurate${NC}"
    echo ""
    echo -e "${YELLOW}📋 READY FOR MANUAL TESTING:${NC}"
    echo -e "${YELLOW}1. Add items to cart via test functionality${NC}"
    echo -e "${YELLOW}2. Proceed to checkout${NC}"
    echo -e "${YELLOW}3. Test payment method selection${NC}"
    echo -e "${YELLOW}4. Complete order and verify rebate calculation${NC}"
    
elif [ ${#CRITICAL_ISSUES[@]} -le 2 ]; then
    echo -e "${YELLOW}⚠️ CHECKOUT SYSTEM STATUS: NEEDS ATTENTION${NC}"
    echo -e "${YELLOW}📋 Minor issues detected but mostly functional${NC}"
    echo -e "${YELLOW}🔧 Address critical issues before production use${NC}"
    
else
    echo -e "${RED}🚨 CHECKOUT SYSTEM STATUS: CRITICAL ISSUES${NC}"
    echo -e "${RED}📋 Multiple critical problems detected${NC}"
    echo -e "${RED}🔧 Immediate fixes required before testing${NC}"
fi

echo ""
echo -e "${BLUE}📋 NEXT STEPS:${NC}"
echo "1. Address any critical issues identified above"
echo "2. Run manual checkout testing with real cart items"
echo "3. Verify rebate calculations with actual member accounts"
echo "4. Test all payment methods (Cash/Store Pickup/GCash)"
echo "5. Confirm order completion and confirmation messages"

# Clean up
rm -f /tmp/test_checkout_db.php /tmp/test_rebate_calculation.php
