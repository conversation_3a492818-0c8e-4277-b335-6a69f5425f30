<?php
/**
 * ExtremeLife MLM System - Enhanced Unilevel Genealogy Tree Visualization
 * Unlimited Width Genealogy Tree with 6-Level Commission Structure
 * Version: 2.0.0 Enhanced - FIXED
 */

session_start();

// Include required files
require_once 'includes/auth.php';

// Check authentication
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Include commission engine if it exists
if (file_exists('ExtremeLifeUnilevelCommissionEngine.php')) {
    require_once 'ExtremeLifeUnilevelCommissionEngine.php';
}

// Initialize commission engine if available
$commission_engine = null;
$genealogy_tree = [];

if (class_exists('ExtremeLifeUnilevelCommissionEngine')) {
    try {
        $commission_engine = new ExtremeLifeUnilevelCommissionEngine([
            'host' => 'localhost',
            'dbname' => 'drupal_umd',
            'username' => 'drupal_user',
            'password' => 'secure_drupal_pass_1748318545'
        ]);
    } catch (Exception $e) {
        error_log("Commission engine initialization failed: " . $e->getMessage());
    }
}

// Get member ID (current user or specified member)
$member_id = $_GET['member_id'] ?? $_SESSION['member_id'] ?? 1;
$max_depth = $_GET['depth'] ?? 6;

// Get genealogy tree data
if ($commission_engine) {
    try {
        $genealogy_tree = $commission_engine->getGenealogyTree($member_id, $max_depth);
    } catch (Exception $e) {
        error_log("Failed to get genealogy tree: " . $e->getMessage());
        $genealogy_tree = [];
    }
}

// Get member information
try {
    $pdo = new PDO("mysql:host=localhost;dbname=drupal_umd", "drupal_user", "secure_drupal_pass_1748318545");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->prepare("
        SELECT m.*, ug.group_name,
               (SELECT COUNT(*) FROM mlm_members WHERE sponsor_id = m.id AND status = 'active') as direct_referrals,
               (SELECT SUM(group_sales_volume) FROM mlm_members WHERE sponsor_id = m.id) as team_sales
        FROM mlm_members m
        LEFT JOIN mlm_user_groups ug ON m.user_group_id = ug.id
        WHERE m.id = ?
    ");
    $stmt->execute([$member_id]);
    $root_member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get all active members for dropdown
    $stmt = $pdo->query("
        SELECT id, first_name, last_name, referral_code, current_rank
        FROM mlm_members 
        WHERE status = 'active'
        ORDER BY first_name, last_name
    ");
    $all_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = "Database error: " . $e->getMessage();
}

// Handle AJAX requests for tree data
if (isset($_GET['ajax']) && $_GET['ajax'] === 'tree_data') {
    header('Content-Type: application/json');
    echo json_encode([
        'root_member' => $root_member,
        'genealogy_tree' => $genealogy_tree,
        'max_depth' => $max_depth
    ]);
    exit;
}

// Add getMemberInfo function for e-commerce integration
function getMemberInfo($member_id) {
    // Include database configuration
    $config_file = dirname(__DIR__) . '/config/database.php';
    if (file_exists($config_file)) {
        $config = include $config_file;
    } else {
        return [
            'id' => $member_id,
            'first_name' => 'Member',
            'last_name' => '',
            'user_group_id' => 1,
            'group_name' => 'Member',
            'group_commission_rate' => 10
        ];
    }

    try {
        $pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']}", $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("
            SELECT m.*, ug.group_name, ug.commission_rate as group_commission_rate
            FROM mlm_members m
            LEFT JOIN mlm_user_groups ug ON m.user_group_id = ug.id
            WHERE m.id = ?
        ");
        $stmt->execute([$member_id]);
        $result = $stmt->fetch();

        if (!$result) {
            return [
                'id' => $member_id,
                'first_name' => 'Member',
                'last_name' => '',
                'user_group_id' => 1,
                'group_name' => 'Member',
                'group_commission_rate' => 10
            ];
        }

        return $result;
    } catch (PDOException $e) {
        error_log("Error getting member info: " . $e->getMessage());
        return [
            'id' => $member_id,
            'first_name' => 'Member',
            'last_name' => '',
            'user_group_id' => 1,
            'group_name' => 'Member',
            'group_commission_rate' => 10
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Genealogy Tree - ExtremeLife MLM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2d5a27;
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .header .logo {
            margin-right: 15px;
            font-size: 1.2em;
        }
        
        .controls {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 8px;
            font-weight: bold;
            color: #2d5a27;
        }
        
        .form-group select,
        .form-group input {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #2d5a27;
        }
        
        .btn {
            background: #2d5a27;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background 0.3s ease;
            min-height: 44px;
        }
        
        .btn:hover {
            background: #4a7c59;
        }
        
        .tree-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow-x: auto;
            min-height: 600px;
        }
        
        .tree {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 100%;
        }
        
        .tree-level {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 15px;
            width: 100%;
        }
        
        .member-node {
            background: white;
            border: 3px solid #2d5a27;
            border-radius: 15px;
            padding: 15px;
            min-width: 200px;
            max-width: 250px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .member-node:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .member-node.root {
            border-color: #dc3545;
            background: linear-gradient(135deg, #fff, #f8f9fa);
        }
        
        .member-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2d5a27, #4a7c59);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin: 0 auto 10px;
        }
        
        .member-name {
            font-weight: bold;
            color: #2d5a27;
            margin-bottom: 5px;
            font-size: 1.1em;
        }
        
        .member-code {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .member-rank {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            color: white;
            margin-bottom: 8px;
            display: inline-block;
        }
        
        .rank-associate { background: #6c757d; }
        .rank-builder { background: #17a2b8; }
        .rank-leader { background: #28a745; }
        .rank-elite-leader { background: #ffc107; color: #212529; }
        .rank-director { background: #fd7e14; }
        .rank-executive { background: #dc3545; }
        
        .member-stats {
            font-size: 0.8em;
            color: #666;
            margin-top: 8px;
        }
        
        .member-stats div {
            margin: 2px 0;
        }
        
        .level-indicator {
            background: #2d5a27;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 20px 0;
            display: inline-block;
        }
        
        .connection-line {
            width: 2px;
            height: 30px;
            background: #2d5a27;
            margin: 0 auto;
        }
        
        .horizontal-line {
            height: 2px;
            background: #2d5a27;
            margin: 10px 0;
            width: 100%;
        }
        
        .stats-summary {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-card {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 4px solid #2d5a27;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2d5a27;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2d5a27;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-level {
            color: #999;
            font-style: italic;
            padding: 20px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .controls {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .member-node {
                min-width: 180px;
                max-width: 200px;
                padding: 12px;
            }
            
            .member-avatar {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
            
            .stats-summary {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <span class="logo">🌿</span>
                ExtremeLife MLM Genealogy Tree
            </h1>
            <p>Unlimited Width Unilevel Structure - 6 Level Commission System</p>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="form-group">
                <label for="member-select">Select Member:</label>
                <select id="member-select" onchange="loadMemberTree()">
                    <?php foreach ($all_members as $member): ?>
                        <option value="<?= $member['id'] ?>" <?= $member['id'] == $member_id ? 'selected' : '' ?>>
                            <?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?> 
                            (<?= $member['referral_code'] ?>) - <?= $member['current_rank'] ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="depth-select">Tree Depth:</label>
                <select id="depth-select" onchange="loadMemberTree()">
                    <option value="3" <?= $max_depth == 3 ? 'selected' : '' ?>>3 Levels</option>
                    <option value="6" <?= $max_depth == 6 ? 'selected' : '' ?>>6 Levels (Full)</option>
                    <option value="10" <?= $max_depth == 10 ? 'selected' : '' ?>>10 Levels</option>
                </select>
            </div>
            
            <div class="form-group">
                <button class="btn" onclick="loadMemberTree()">Load Tree</button>
            </div>
            
            <div class="form-group">
                <button class="btn" onclick="exportTree()">Export Tree</button>
            </div>
        </div>

        <!-- Statistics Summary -->
        <?php if ($root_member): ?>
        <div class="stats-summary">
            <div class="stat-card">
                <div class="stat-value"><?= $root_member['direct_referrals'] ?></div>
                <div class="stat-label">Direct Referrals</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?= count($genealogy_tree) ?></div>
                <div class="stat-label">Total Team Size</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">₱<?= number_format($root_member['group_sales_volume'] ?? 0, 0) ?></div>
                <div class="stat-label">Group Sales Volume</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">₱<?= number_format($root_member['total_earnings'] ?? 0, 2) ?></div>
                <div class="stat-label">Total Earnings</div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Genealogy Tree -->
        <div class="tree-container">
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Loading genealogy tree...</p>
            </div>
            
            <div id="tree-content">
                <?php if ($root_member): ?>
                    <!-- Root Member -->
                    <div class="tree">
                        <div class="member-node root" onclick="viewMemberDetails(<?= $root_member['id'] ?>)">
                            <div class="member-avatar">
                                <?= strtoupper(substr($root_member['first_name'], 0, 1) . substr($root_member['last_name'], 0, 1)) ?>
                            </div>
                            <div class="member-name"><?= htmlspecialchars($root_member['first_name'] . ' ' . $root_member['last_name']) ?></div>
                            <div class="member-code"><?= $root_member['referral_code'] ?></div>
                            <div class="member-rank rank-<?= strtolower(str_replace(' ', '-', $root_member['current_rank'])) ?>">
                                <?= $root_member['current_rank'] ?>
                            </div>
                            <div class="member-stats">
                                <div>Referrals: <?= $root_member['direct_referrals'] ?></div>
                                <div>Sales: ₱<?= number_format($root_member['personal_sales_volume'] ?? 0, 0) ?></div>
                            </div>
                        </div>
                        
                        <?php if (!empty($genealogy_tree)): ?>
                            <div class="connection-line"></div>
                            <?php 
                            // Group members by level
                            $levels = [];
                            foreach ($genealogy_tree as $member) {
                                $levels[$member['level']][] = $member;
                            }
                            
                            for ($level = 1; $level <= $max_depth; $level++):
                                if (isset($levels[$level]) && !empty($levels[$level])):
                            ?>
                                <div class="level-indicator">Level <?= $level ?></div>
                                <div class="tree-level">
                                    <?php foreach ($levels[$level] as $member): ?>
                                        <div class="member-node" onclick="viewMemberDetails(<?= $member['id'] ?>)">
                                            <div class="member-avatar">
                                                <?= strtoupper(substr($member['first_name'], 0, 1) . substr($member['last_name'], 0, 1)) ?>
                                            </div>
                                            <div class="member-name"><?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?></div>
                                            <div class="member-code"><?= $member['referral_code'] ?></div>
                                            <div class="member-rank rank-<?= strtolower(str_replace(' ', '-', $member['current_rank'])) ?>">
                                                <?= $member['current_rank'] ?>
                                            </div>
                                            <div class="member-stats">
                                                <div>Referrals: <?= $member['direct_referrals'] ?></div>
                                                <div>Sales: ₱<?= number_format($member['personal_sales_volume'] ?? 0, 0) ?></div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if ($level < $max_depth): ?>
                                    <div class="connection-line"></div>
                                <?php endif; ?>
                            <?php 
                                else:
                            ?>
                                <div class="level-indicator">Level <?= $level ?></div>
                                <div class="empty-level">No members at this level</div>
                            <?php 
                                endif;
                            endfor; 
                            ?>
                        <?php else: ?>
                            <div class="empty-level">No downline members found</div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-level">Member not found</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function loadMemberTree() {
            const memberId = document.getElementById('member-select').value;
            const depth = document.getElementById('depth-select').value;
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('tree-content').style.display = 'none';
            
            // Redirect to load new tree
            window.location.href = `genealogy_tree_unilevel.php?member_id=${memberId}&depth=${depth}`;
        }
        
        function viewMemberDetails(memberId) {
            // Open member details in new window or modal
            window.open(`member_details.php?id=${memberId}`, '_blank', 'width=800,height=600');
        }
        
        function exportTree() {
            // Export tree as image or PDF
            alert('Export functionality would be implemented here');
        }
        
        // Auto-refresh tree every 5 minutes
        setInterval(() => {
            loadMemberTree();
        }, 300000);
    </script>
</body>
</html>
