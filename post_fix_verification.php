<?php
/**
 * ExtremeLife MLM Post-Fix Verification Script
 * Verifies that all deployment fixes have been applied correctly
 */

echo "🔍 ExtremeLife MLM Post-Fix Verification\n";
echo "========================================\n\n";

$base_url = "https://extremelifeherbal.com";
$mlm_url = $base_url . "/mlm";
$total_tests = 0;
$passed_tests = 0;
$issues = [];

function verifyUrl($url, $test_name, $expected_status = 200, $should_contain = null, $should_not_contain = null) {
    global $total_tests, $passed_tests, $issues;
    $total_tests++;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'ExtremeLife MLM Verification Tool');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ $test_name - CURL Error: $error\n";
        $issues[] = "$test_name - CURL Error: $error";
        return false;
    }
    
    $status_ok = ($http_code === $expected_status);
    $content_ok = true;
    
    if ($should_contain && $status_ok) {
        $content_ok = (strpos($response, $should_contain) !== false);
    }
    
    if ($should_not_contain && $status_ok) {
        $content_ok = $content_ok && (strpos($response, $should_not_contain) === false);
    }
    
    if ($status_ok && $content_ok) {
        echo "✅ $test_name - HTTP $http_code";
        if ($should_contain) echo " (Contains: '$should_contain')";
        if ($should_not_contain) echo " (Excludes: '$should_not_contain')";
        echo "\n";
        $passed_tests++;
        return true;
    } else {
        echo "❌ $test_name - HTTP $http_code";
        if (!$status_ok) echo " (Expected: $expected_status)";
        if ($should_contain && !$content_ok) echo " (Missing: '$should_contain')";
        if ($should_not_contain && !$content_ok) echo " (Contains: '$should_not_contain')";
        echo "\n";
        $issues[] = "$test_name - Failed verification";
        return false;
    }
}

echo "📁 FILE LOCATION VERIFICATION\n";
echo "=============================\n";

// Verify all files are in correct MLM directory
verifyUrl("$mlm_url/member_dashboard.php", "MLM Member Dashboard");
verifyUrl("$mlm_url/register.php", "MLM Registration Page");
verifyUrl("$mlm_url/enhanced_cart.php", "MLM Enhanced Cart");
verifyUrl("$mlm_url/login.php", "MLM Login Page");
verifyUrl("$mlm_url/genealogy_tree_unilevel.php", "MLM Genealogy Tree");

echo "\n🚫 DUPLICATE FILE VERIFICATION\n";
echo "==============================\n";

// Verify duplicates are removed from root
verifyUrl("$base_url/login.php", "Root Login Removal", 404);

echo "\n🔐 SECURITY VERIFICATION\n";
echo "========================\n";

// Verify config and includes are secured
verifyUrl("$mlm_url/config/database.php", "Config Security", 403);
verifyUrl("$mlm_url/includes/auth.php", "Includes Security", 403);

echo "\n🎨 BRANDING AND FUNCTIONALITY VERIFICATION\n";
echo "==========================================\n";

// Verify branding and functionality
verifyUrl("$mlm_url/login.php", "ExtremeLife Branding", 200, "ExtremeLife");
verifyUrl("$mlm_url/register.php", "Philippine Peso Currency", 200, "₱");
verifyUrl("$mlm_url/enhanced_cart.php", "Cart Functionality", 200, "Cart");

echo "\n🔧 AUTHENTICATION SYSTEM VERIFICATION\n";
echo "=====================================\n";

// Verify authentication system
verifyUrl("$mlm_url/genealogy_tree_unilevel.php", "Auth Redirect", 200, "login");
verifyUrl("$mlm_url/member_dashboard.php", "Dashboard Auth", 200, "login");

echo "\n📊 ADMIN INTERFACE VERIFICATION\n";
echo "===============================\n";

// Verify admin interfaces
verifyUrl("$mlm_url/admin/admin_commission_management.php", "Admin Commission Management");
verifyUrl("$mlm_url/admin/login.php", "Admin Login");

echo "\n🌐 NAVIGATION AND INTEGRATION VERIFICATION\n";
echo "==========================================\n";

// Verify navigation links work
verifyUrl("$mlm_url/", "MLM Directory Access");
verifyUrl("$mlm_url/index.php", "MLM Index Page");

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 POST-FIX VERIFICATION SUMMARY\n";
echo str_repeat("=", 60) . "\n";

echo "Total Verification Tests: $total_tests\n";
echo "Passed: $passed_tests\n";
echo "Failed: " . ($total_tests - $passed_tests) . "\n";
echo "Success Rate: " . round(($passed_tests / $total_tests) * 100, 1) . "%\n\n";

if (count($issues) > 0) {
    echo "🚨 REMAINING ISSUES:\n";
    echo str_repeat("=", 20) . "\n";
    foreach ($issues as $issue) {
        echo "• $issue\n";
    }
    echo "\n";
}

echo "📊 DEPLOYMENT STATUS:\n";
echo str_repeat("=", 20) . "\n";

if ($passed_tests === $total_tests) {
    echo "🎉 PERFECT: All fixes applied successfully!\n";
    echo "✅ System is 100% functional\n";
    echo "✅ All files in correct locations\n";
    echo "✅ Security properly configured\n";
    echo "✅ No duplicate files\n";
    echo "✅ Branding and functionality intact\n\n";
    
    echo "🚀 SYSTEM READY FOR PRODUCTION USE\n";
    
} elseif ($passed_tests >= ($total_tests * 0.9)) {
    echo "✅ EXCELLENT: Most fixes applied successfully\n";
    echo "🟡 Minor issues remaining - review above\n";
    echo "📋 System is production-ready with minor tweaks needed\n\n";
    
} elseif ($passed_tests >= ($total_tests * 0.8)) {
    echo "🟡 GOOD: Major fixes applied\n";
    echo "⚠️ Some issues remaining - review above\n";
    echo "📋 Additional deployment work needed\n\n";
    
} else {
    echo "🔴 POOR: Fixes not properly applied\n";
    echo "🚨 Critical issues remain - review above\n";
    echo "📋 Re-run deployment fix procedures\n\n";
}

echo "📋 NEXT STEPS:\n";
if ($passed_tests === $total_tests) {
    echo "1. ✅ System fully operational - no further action needed\n";
    echo "2. 📊 Monitor system performance\n";
    echo "3. 🔄 Set up regular backups\n";
    echo "4. 📈 Begin user acceptance testing\n";
} else {
    echo "1. 🔧 Address remaining issues listed above\n";
    echo "2. 🔄 Re-run deployment fix procedures\n";
    echo "3. 🧪 Re-run this verification script\n";
    echo "4. 📞 Contact system administrator if issues persist\n";
}

echo "\n📞 SUPPORT INFORMATION:\n";
echo "Server: ***************\n";
echo "Domain: extremelifeherbal.com\n";
echo "MLM Path: /var/www/html/mlm/\n";
echo "SSH User: mlmadmin\n";
?>
