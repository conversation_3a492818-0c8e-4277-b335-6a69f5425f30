#!/bin/bash

# Fix URL Redirects - ExtremeLife MLM Production
# Creates redirects for files moved from root to /mlm/ directory

echo "🔧 Fixing URL Redirects for ExtremeLife MLM"
echo "==========================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo -e "${YELLOW}Issue: Files moved to /mlm/ but old URLs still referenced${NC}"
echo -e "${YELLOW}Solution: Create redirect files in root directory${NC}"
echo ""

# Create redirect files in root directory for moved files

print_info "Creating redirect files in root directory..."

# 1. Create enhanced_cart.php redirect
print_info "Creating enhanced_cart.php redirect..."
sudo cat > /var/www/html/enhanced_cart.php << 'EOF'
<?php
/**
 * ExtremeLife MLM - Enhanced Cart Redirect
 * Redirects to new location in /mlm/ directory
 */

// Permanent redirect to new location
header('HTTP/1.1 301 Moved Permanently');
header('Location: /mlm/enhanced_cart.php');
exit;
?>
EOF

# 2. Create member_dashboard.php redirect
print_info "Creating member_dashboard.php redirect..."
sudo cat > /var/www/html/member_dashboard.php << 'EOF'
<?php
/**
 * ExtremeLife MLM - Member Dashboard Redirect
 * Redirects to new location in /mlm/ directory
 */

// Permanent redirect to new location
header('HTTP/1.1 301 Moved Permanently');
header('Location: /mlm/member_dashboard.php');
exit;
?>
EOF

# 3. Create login.php redirect
print_info "Creating login.php redirect..."
sudo cat > /var/www/html/login.php << 'EOF'
<?php
/**
 * ExtremeLife MLM - Login Redirect
 * Redirects to new location in /mlm/ directory
 */

// Permanent redirect to new location
header('HTTP/1.1 301 Moved Permanently');
header('Location: /mlm/login.php');
exit;
?>
EOF

# 4. Create register.php redirect (if needed)
print_info "Creating register.php redirect..."
sudo cat > /var/www/html/register.php << 'EOF'
<?php
/**
 * ExtremeLife MLM - Registration Redirect
 * Redirects to new location in /mlm/ directory
 */

// Permanent redirect to new location
header('HTTP/1.1 301 Moved Permanently');
header('Location: /mlm/register.php');
exit;
?>
EOF

# 5. Create genealogy_tree.php redirect (common reference)
print_info "Creating genealogy_tree.php redirect..."
sudo cat > /var/www/html/genealogy_tree.php << 'EOF'
<?php
/**
 * ExtremeLife MLM - Genealogy Tree Redirect
 * Redirects to new location in /mlm/ directory
 */

// Permanent redirect to new location
header('HTTP/1.1 301 Moved Permanently');
header('Location: /mlm/genealogy_tree_unilevel.php');
exit;
?>
EOF

# Set proper permissions for redirect files
print_info "Setting proper permissions for redirect files..."
sudo chmod 644 /var/www/html/enhanced_cart.php
sudo chmod 644 /var/www/html/member_dashboard.php
sudo chmod 644 /var/www/html/login.php
sudo chmod 644 /var/www/html/register.php
sudo chmod 644 /var/www/html/genealogy_tree.php

# Set proper ownership
sudo chown www-data:www-data /var/www/html/enhanced_cart.php
sudo chown www-data:www-data /var/www/html/member_dashboard.php
sudo chown www-data:www-data /var/www/html/login.php
sudo chown www-data:www-data /var/www/html/register.php
sudo chown www-data:www-data /var/www/html/genealogy_tree.php

print_status "All redirect files created and configured"

# Create .htaccess redirect rules as backup
print_info "Creating .htaccess redirect rules..."
sudo cat > /var/www/html/.htaccess << 'EOF'
# ExtremeLife MLM URL Redirects
# Redirect old root URLs to new /mlm/ locations

RewriteEngine On

# Redirect specific MLM files to /mlm/ directory
RewriteRule ^enhanced_cart\.php$ /mlm/enhanced_cart.php [R=301,L]
RewriteRule ^member_dashboard\.php$ /mlm/member_dashboard.php [R=301,L]
RewriteRule ^login\.php$ /mlm/login.php [R=301,L]
RewriteRule ^register\.php$ /mlm/register.php [R=301,L]
RewriteRule ^genealogy_tree\.php$ /mlm/genealogy_tree_unilevel.php [R=301,L]
RewriteRule ^genealogy_tree_unilevel\.php$ /mlm/genealogy_tree_unilevel.php [R=301,L]

# Redirect admin URLs
RewriteRule ^admin/(.*)$ /mlm/admin/$1 [R=301,L]
EOF

sudo chmod 644 /var/www/html/.htaccess
sudo chown www-data:www-data /var/www/html/.htaccess

print_status ".htaccess redirect rules created"

# Test Apache configuration
print_info "Testing Apache configuration..."
if sudo apache2ctl configtest > /tmp/apache_test 2>&1; then
    print_status "Apache configuration is valid"
else
    print_warning "Apache configuration issues detected:"
    cat /tmp/apache_test
fi

# Restart Apache to apply changes
print_info "Restarting Apache to apply redirect rules..."
sudo systemctl restart apache2

if sudo systemctl is-active --quiet apache2; then
    print_status "Apache restarted successfully"
else
    print_warning "Apache restart failed - checking status..."
    sudo systemctl status apache2
fi

# Test the redirects
print_info "Testing redirect functionality..."

echo ""
echo "Testing redirects with curl:"

# Test enhanced_cart.php redirect
CART_TEST=$(curl -s -I https://extremelifeherbal.com/enhanced_cart.php 2>/dev/null | grep -i "HTTP\|Location" | head -2)
echo "enhanced_cart.php redirect:"
echo "$CART_TEST"

# Test member_dashboard.php redirect
DASHBOARD_TEST=$(curl -s -I https://extremelifeherbal.com/member_dashboard.php 2>/dev/null | grep -i "HTTP\|Location" | head -2)
echo "member_dashboard.php redirect:"
echo "$DASHBOARD_TEST"

# Test login.php redirect
LOGIN_TEST=$(curl -s -I https://extremelifeherbal.com/login.php 2>/dev/null | grep -i "HTTP\|Location" | head -2)
echo "login.php redirect:"
echo "$LOGIN_TEST"

echo ""
echo -e "${GREEN}🎯 URL REDIRECT FIX COMPLETED${NC}"
echo -e "${GREEN}=============================${NC}"
echo ""
echo -e "${YELLOW}📋 CHANGES MADE:${NC}"
echo -e "${YELLOW}• Created redirect files in root directory${NC}"
echo -e "${YELLOW}• Added .htaccess redirect rules${NC}"
echo -e "${YELLOW}• Set proper file permissions${NC}"
echo -e "${YELLOW}• Restarted Apache service${NC}"
echo ""
echo -e "${BLUE}🧪 TEST URLS NOW:${NC}"
echo -e "${BLUE}• https://extremelifeherbal.com/enhanced_cart.php${NC}"
echo -e "${BLUE}• https://extremelifeherbal.com/member_dashboard.php${NC}"
echo -e "${BLUE}• https://extremelifeherbal.com/login.php${NC}"
echo -e "${BLUE}• https://extremelifeherbal.com/register.php${NC}"
echo ""
echo -e "${GREEN}🚀 All URLs should now redirect to /mlm/ directory${NC}"

# Clean up
rm -f /tmp/apache_test
