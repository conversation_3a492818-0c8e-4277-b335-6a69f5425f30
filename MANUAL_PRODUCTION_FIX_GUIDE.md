# 🚨 MANUAL PRODUCTION FIX GUIDE - ExtremeLife MLM

## **CRITICAL DEPLOYMENT FIXES FOR PRODUCTION SERVER**

**Server:** *************** (extremelifeherbal.com)  
**Goal:** Achieve 95%+ system functionality  
**Current:** 57.1% functionality (mixed deployment)  
**Estimated Time:** 20 minutes

---

## 🔐 **STEP 1: SSH CONNECTION**

```bash
# Connect to production server
ssh mlmadmin@***************
# Password: 4K-6GsnA$3pQ5931

# Verify current location
pwd
# Should show: /home/<USER>

# Switch to web directory
cd /var/www/html
```

---

## 💾 **STEP 2: CREATE BACKUP (CRITICAL)**

```bash
# Create timestamped backup
sudo cp -r /var/www/html /var/www/html_backup_$(date +%Y%m%d_%H%M%S)

# Verify backup created
ls -la /var/www/html_backup_*

# Note: Keep this backup location for rollback if needed
```

---

## 📁 **STEP 3: FILE REORGANIZATION**

### **Move Files from Root to MLM Directory:**

```bash
# Check current file locations
ls -la /var/www/html/member_dashboard.php
ls -la /var/www/html/register.php
ls -la /var/www/html/enhanced_cart.php
ls -la /var/www/html/login.php

# Move files to MLM directory
sudo mv /var/www/html/member_dashboard.php /var/www/html/mlm/
sudo mv /var/www/html/register.php /var/www/html/mlm/
sudo mv /var/www/html/enhanced_cart.php /var/www/html/mlm/

# Remove duplicate login.php from root (keep MLM version)
sudo rm /var/www/html/login.php

# Verify files moved correctly
ls -la /var/www/html/mlm/
```

**Expected Result:**
```
✅ /var/www/html/mlm/member_dashboard.php
✅ /var/www/html/mlm/register.php
✅ /var/www/html/mlm/enhanced_cart.php
✅ /var/www/html/mlm/login.php
✅ /var/www/html/mlm/genealogy_tree_unilevel.php
❌ /var/www/html/login.php (should be removed)
```

---

## 🔒 **STEP 4: SECURITY CONFIGURATION**

### **Secure Config Directory:**

```bash
# Create config directory if not exists
sudo mkdir -p /var/www/html/mlm/config

# Create .htaccess to deny web access
sudo cat > /var/www/html/mlm/config/.htaccess << 'EOF'
Order deny,allow
Deny from all
EOF
```

### **Secure Includes Directory:**

```bash
# Create includes directory if not exists
sudo mkdir -p /var/www/html/mlm/includes

# Create .htaccess to deny web access
sudo cat > /var/www/html/mlm/includes/.htaccess << 'EOF'
Order deny,allow
Deny from all
EOF
```

---

## 🔧 **STEP 5: FILE PERMISSIONS AND OWNERSHIP**

```bash
# Set proper ownership
sudo chown -R www-data:www-data /var/www/html/mlm/

# Set file permissions (644 for PHP files)
sudo find /var/www/html/mlm/ -type f -name "*.php" -exec chmod 644 {} \;

# Set directory permissions (755 for directories)
sudo find /var/www/html/mlm/ -type d -exec chmod 755 {} \;

# Set .htaccess permissions
sudo chmod 644 /var/www/html/mlm/config/.htaccess
sudo chmod 644 /var/www/html/mlm/includes/.htaccess

# Verify permissions
ls -la /var/www/html/mlm/
ls -la /var/www/html/mlm/config/
ls -la /var/www/html/mlm/includes/
```

---

## 📄 **STEP 6: CREATE MLM INDEX FILE**

```bash
# Create index.php for MLM directory
sudo cat > /var/www/html/mlm/index.php << 'EOF'
<?php
/**
 * ExtremeLife MLM System - Main Directory Index
 * Redirects to login page for security
 */

session_start();

// Check if user is already logged in
if (isset($_SESSION['member_id'])) {
    header('Location: member_dashboard.php');
    exit;
}

// Redirect non-logged-in users to login
header('Location: login.php');
exit;
?>
EOF

# Set proper ownership and permissions
sudo chown www-data:www-data /var/www/html/mlm/index.php
sudo chmod 644 /var/www/html/mlm/index.php
```

---

## 🔄 **STEP 7: RESTART APACHE**

```bash
# Restart Apache web server
sudo systemctl restart apache2

# Check Apache status
sudo systemctl status apache2

# Verify Apache is running
sudo systemctl is-active apache2
# Should return: active
```

---

## ✅ **STEP 8: IMMEDIATE VERIFICATION**

### **Test File Locations:**

```bash
# Check all files are in correct locations
ls -la /var/www/html/mlm/member_dashboard.php
ls -la /var/www/html/mlm/register.php
ls -la /var/www/html/mlm/enhanced_cart.php
ls -la /var/www/html/mlm/login.php
ls -la /var/www/html/mlm/genealogy_tree_unilevel.php
ls -la /var/www/html/mlm/index.php

# Verify no duplicates in root
ls -la /var/www/html/login.php
# Should return: No such file or directory
```

### **Test Security Configuration:**

```bash
# Check .htaccess files exist
ls -la /var/www/html/mlm/config/.htaccess
ls -la /var/www/html/mlm/includes/.htaccess

# Test web access (should be denied)
curl -I https://extremelifeherbal.com/mlm/config/database.php
# Should return: HTTP/1.1 403 Forbidden

curl -I https://extremelifeherbal.com/mlm/includes/auth.php
# Should return: HTTP/1.1 403 Forbidden
```

---

## 🧪 **STEP 9: COMPREHENSIVE TESTING**

### **Test Core URLs:**

```bash
# Test MLM directory access
curl -I https://extremelifeherbal.com/mlm/
# Should return: HTTP/1.1 200 OK (redirect to login)

# Test core pages
curl -I https://extremelifeherbal.com/mlm/login.php
curl -I https://extremelifeherbal.com/mlm/register.php
curl -I https://extremelifeherbal.com/mlm/member_dashboard.php
curl -I https://extremelifeherbal.com/mlm/enhanced_cart.php
curl -I https://extremelifeherbal.com/mlm/genealogy_tree_unilevel.php
# All should return: HTTP/1.1 200 OK
```

---

## 📊 **STEP 10: RUN VERIFICATION SCRIPT**

```bash
# Upload and run the post_fix_verification.php script
# (Upload the script to server first)

php post_fix_verification.php
```

**Expected Results:**
- **Success Rate:** 95%+ (20+/21 tests passing)
- **File Organization:** All files in /mlm/ directory
- **Security:** Config/includes return 403
- **Functionality:** All core features working

---

## 🚨 **ROLLBACK PROCEDURE (IF NEEDED)**

```bash
# If something goes wrong, restore from backup
sudo rm -rf /var/www/html
sudo cp -r /var/www/html_backup_YYYYMMDD_HHMMSS /var/www/html
sudo systemctl restart apache2
```

---

## 🎯 **SUCCESS CRITERIA**

### **✅ Files Correctly Located:**
- All MLM files in `/var/www/html/mlm/` directory
- No duplicate files in root directory
- Proper index.php for MLM directory

### **✅ Security Configured:**
- Config directory returns HTTP 403
- Includes directory returns HTTP 403
- Sensitive files not web-accessible

### **✅ System Functional:**
- All core URLs return HTTP 200
- Authentication system working
- ExtremeLife branding intact
- Philippine Peso currency displaying

---

## 📞 **SUPPORT INFORMATION**

**If Issues Occur:**
1. Check Apache error logs: `sudo tail -f /var/log/apache2/error.log`
2. Verify file permissions: `ls -la /var/www/html/mlm/`
3. Test individual URLs with curl
4. Use rollback procedure if necessary

**Server Details:**
- **IP:** ***************
- **Domain:** extremelifeherbal.com
- **SSH User:** mlmadmin
- **Web Directory:** /var/www/html/mlm/

---

**🚀 EXECUTE THESE STEPS TO ACHIEVE 95%+ SYSTEM FUNCTIONALITY**
