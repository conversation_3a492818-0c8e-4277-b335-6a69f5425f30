#!/bin/bash

# Quick Verification Test for ExtremeLife MLM Production Fixes
# Run this script after implementing the fixes to verify success

echo "🔍 ExtremeLife MLM Quick Verification Test"
echo "=========================================="
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

PASSED=0
FAILED=0
TOTAL=0

test_result() {
    TOTAL=$((TOTAL + 1))
    if [ $1 -eq 0 ]; then
        echo -e "✅ $2"
        PASSED=$((PASSED + 1))
    else
        echo -e "❌ $2"
        FAILED=$((FAILED + 1))
    fi
}

echo "📁 FILE LOCATION VERIFICATION"
echo "============================="

# Check if files exist in MLM directory
test -f /var/www/html/mlm/member_dashboard.php
test_result $? "member_dashboard.php in MLM directory"

test -f /var/www/html/mlm/register.php
test_result $? "register.php in MLM directory"

test -f /var/www/html/mlm/enhanced_cart.php
test_result $? "enhanced_cart.php in MLM directory"

test -f /var/www/html/mlm/login.php
test_result $? "login.php in MLM directory"

test -f /var/www/html/mlm/genealogy_tree_unilevel.php
test_result $? "genealogy_tree_unilevel.php in MLM directory"

test -f /var/www/html/mlm/index.php
test_result $? "index.php in MLM directory"

echo ""
echo "🚫 DUPLICATE FILE VERIFICATION"
echo "=============================="

# Check that duplicates are removed from root
test ! -f /var/www/html/login.php
test_result $? "Duplicate login.php removed from root"

echo ""
echo "🔒 SECURITY VERIFICATION"
echo "========================"

# Check security files exist
test -f /var/www/html/mlm/config/.htaccess
test_result $? "Config .htaccess security file exists"

test -f /var/www/html/mlm/includes/.htaccess
test_result $? "Includes .htaccess security file exists"

echo ""
echo "🔧 PERMISSIONS VERIFICATION"
echo "==========================="

# Check ownership
OWNER=$(stat -c '%U:%G' /var/www/html/mlm/login.php 2>/dev/null)
if [ "$OWNER" = "www-data:www-data" ]; then
    test_result 0 "File ownership is www-data:www-data"
else
    test_result 1 "File ownership is $OWNER (should be www-data:www-data)"
fi

# Check PHP file permissions
PERMS=$(stat -c '%a' /var/www/html/mlm/login.php 2>/dev/null)
if [ "$PERMS" = "644" ]; then
    test_result 0 "PHP file permissions are 644"
else
    test_result 1 "PHP file permissions are $PERMS (should be 644)"
fi

# Check directory permissions
DIR_PERMS=$(stat -c '%a' /var/www/html/mlm/config 2>/dev/null)
if [ "$DIR_PERMS" = "755" ]; then
    test_result 0 "Directory permissions are 755"
else
    test_result 1 "Directory permissions are $DIR_PERMS (should be 755)"
fi

echo ""
echo "🌐 WEB ACCESS VERIFICATION"
echo "=========================="

# Test web access to core pages
curl -s -I https://extremelifeherbal.com/mlm/ | grep -q "200 OK"
test_result $? "MLM directory accessible (HTTP 200)"

curl -s -I https://extremelifeherbal.com/mlm/login.php | grep -q "200 OK"
test_result $? "Login page accessible (HTTP 200)"

curl -s -I https://extremelifeherbal.com/mlm/register.php | grep -q "200 OK"
test_result $? "Registration page accessible (HTTP 200)"

curl -s -I https://extremelifeherbal.com/mlm/member_dashboard.php | grep -q "200 OK"
test_result $? "Member dashboard accessible (HTTP 200)"

curl -s -I https://extremelifeherbal.com/mlm/enhanced_cart.php | grep -q "200 OK"
test_result $? "Enhanced cart accessible (HTTP 200)"

echo ""
echo "🔐 SECURITY ACCESS VERIFICATION"
echo "==============================="

# Test that config files are secured
curl -s -I https://extremelifeherbal.com/mlm/config/database.php | grep -q "403 Forbidden"
test_result $? "Config directory secured (HTTP 403)"

curl -s -I https://extremelifeherbal.com/mlm/includes/auth.php | grep -q "403 Forbidden"
test_result $? "Includes directory secured (HTTP 403)"

echo ""
echo "🔄 APACHE SERVICE VERIFICATION"
echo "=============================="

# Check Apache is running
systemctl is-active --quiet apache2
test_result $? "Apache service is running"

echo ""
echo "========================================"
echo "🎯 VERIFICATION SUMMARY"
echo "========================================"
echo "Total Tests: $TOTAL"
echo "Passed: $PASSED"
echo "Failed: $FAILED"

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED! Production fixes successful!${NC}"
    echo -e "${GREEN}✅ System is now 95%+ functional${NC}"
    echo -e "${GREEN}✅ All files in correct locations${NC}"
    echo -e "${GREEN}✅ Security properly configured${NC}"
    echo -e "${GREEN}✅ No duplicate files${NC}"
    echo ""
    echo -e "${YELLOW}🚀 ExtremeLife MLM system is ready for production use!${NC}"
elif [ $FAILED -le 2 ]; then
    echo -e "${YELLOW}⚠️ Minor issues detected ($FAILED failed tests)${NC}"
    echo -e "${YELLOW}📋 System is mostly functional but review failed tests${NC}"
else
    echo -e "${RED}🚨 Critical issues detected ($FAILED failed tests)${NC}"
    echo -e "${RED}📋 Review and fix failed tests before proceeding${NC}"
fi

echo ""
echo "📊 Success Rate: $(( PASSED * 100 / TOTAL ))%"
echo ""

if [ $FAILED -eq 0 ]; then
    echo "📋 NEXT STEPS:"
    echo "1. ✅ System fully operational"
    echo "2. 🧪 Run comprehensive user acceptance testing"
    echo "3. 📊 Monitor system performance"
    echo "4. 🔄 Set up regular backups"
else
    echo "📋 NEXT STEPS:"
    echo "1. 🔧 Review and fix failed tests above"
    echo "2. 🔄 Re-run this verification script"
    echo "3. 📞 Contact system administrator if issues persist"
fi
